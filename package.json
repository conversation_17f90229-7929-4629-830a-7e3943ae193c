{"name": "security-system", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"ant-design-vue": "^4.0.0-rc.6", "axios": "^1.9.0", "echarts": "^5.6.0", "socket.io-client": "^4.8.1", "vue": "^3.5.13", "vue-data-ui": "^2.10.11", "vue-echarts": "^7.0.3", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "sass-embedded": "^1.89.2", "vite": "^6.3.5"}}