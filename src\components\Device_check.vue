<template>
  <div class="device-check-container">
    <div class="header">
      <h2>设备安全检查</h2>
      <a-button type="primary" @click="showAddModal">创建检查计划</a-button>
    </div>
    
    <a-table :columns="columns" :data-source="checkData" :row-key="record => record.key">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'status'">
          <a-tag :color="getStatusColor(record.status)">{{ record.status }}</a-tag>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <div class="action-buttons">
            <a-button type="primary" size="small" @click="showCheckModal(record)" v-if="record.status === '待检查'">开始检查</a-button>
            <a-button size="small" @click="viewCheckDetails(record)">查看详情</a-button>
            <a-button type="primary" danger size="small" @click="showDeleteConfirm(record)">删除记录</a-button>
          </div>
        </template>
      </template>
    </a-table>

    <!-- 添加检查计划弹窗 -->
    <a-modal
      v-model:visible="addModalVisible"
      title="创建检查计划"
      @ok="handleAddOk"
      @cancel="handleAddCancel"
      okText="确认"
      cancelText="取消"
    >
      <a-form :model="formState" layout="vertical">
        <a-form-item label="设备名称" name="deviceName" :rules="[{ required: true, message: '请选择设备' }]">
          <a-select v-model:value="formState.deviceName" placeholder="选择设备">
            <a-select-option v-for="device in deviceList" :key="device.key" :value="device.name">
              {{ device.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="计划检查日期" name="scheduledDate" :rules="[{ required: true, message: '请选择检查日期' }]">
          <a-date-picker v-model:value="formState.scheduledDate" style="width: 100%" />
        </a-form-item>
        <a-form-item label="检查内容" name="checkItems">
          <a-checkbox-group v-model:value="formState.checkItems">
            <a-row>
              <a-col :span="12">
                <a-checkbox value="外观检查">设备外观</a-checkbox>
              </a-col>
              <a-col :span="12">
                <a-checkbox value="电气线路">电气线路</a-checkbox>
              </a-col>
              <a-col :span="12">
                <a-checkbox value="安全防护装置">安全防护装置</a-checkbox>
              </a-col>
              <a-col :span="12">
                <a-checkbox value="软件系统">软件系统</a-checkbox>
              </a-col>
            </a-row>
          </a-checkbox-group>
        </a-form-item>
        <a-form-item label="负责人员" name="inspector" :rules="[{ required: true, message: '请输入负责人员' }]">
          <a-input v-model:value="formState.inspector" />
        </a-form-item>
        <a-form-item label="备注" name="remark">
          <a-textarea v-model:value="formState.remark" :rows="3" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 检查结果录入弹窗 -->
    <a-modal
      v-model:visible="checkModalVisible"
      title="设备安全检查"
      @ok="handleCheckOk"
      @cancel="handleCheckCancel"
      okText="提交检查结果"
      cancelText="取消"
      width="700px"
    >
      <div v-if="currentCheck">
        <a-descriptions bordered title="检查基本信息" size="small" :column="3">
          <a-descriptions-item label="设备名称">{{ currentCheck.deviceName }}</a-descriptions-item>
          <a-descriptions-item label="计划检查日期">{{ formatDate(currentCheck.scheduledDate) }}</a-descriptions-item>
          <a-descriptions-item label="负责人员">{{ currentCheck.inspector }}</a-descriptions-item>
        </a-descriptions>
        
        <a-divider />
        
        <a-form :model="checkFormState" layout="vertical">
          <h3>检查结果</h3>
          <div v-for="(item, index) in currentCheck.checkItems" :key="index" style="margin-bottom: 16px;">
            <a-card :title="item" size="small">
              <a-form-item :name="['results', index, 'status']" label="检查结果">
                <a-radio-group v-model:value="checkFormState.results[index].status">
                  <a-radio value="正常">正常</a-radio>
                  <a-radio value="异常">异常</a-radio>
                </a-radio-group>
              </a-form-item>
              <a-form-item :name="['results', index, 'description']" label="问题描述" v-if="checkFormState.results[index].status === '异常'">
                <a-textarea v-model:value="checkFormState.results[index].description" :rows="2" />
              </a-form-item>
            </a-card>
          </div>
          
          <a-form-item name="checkDate" label="实际检查日期">
            <a-date-picker v-model:value="checkFormState.checkDate" style="width: 100%" />
          </a-form-item>
          
          <a-form-item name="overallRemark" label="总体评价">
            <a-textarea v-model:value="checkFormState.overallRemark" :rows="3" />
          </a-form-item>
        </a-form>
      </div>
    </a-modal>

    <!-- 检查详情弹窗 -->
    <a-modal
      v-model:visible="detailModalVisible"
      title="检查详情"
      :footer="null"
      width="700px"
    >
      <div v-if="currentCheck">
        <a-descriptions bordered title="检查基本信息" size="small" :column="3">
          <a-descriptions-item label="设备名称">{{ currentCheck.deviceName }}</a-descriptions-item>
          <a-descriptions-item label="计划检查日期">{{ formatDate(currentCheck.scheduledDate) }}</a-descriptions-item>
          <a-descriptions-item label="负责人员">{{ currentCheck.inspector }}</a-descriptions-item>
          <a-descriptions-item label="检查状态">
            <a-tag :color="getStatusColor(currentCheck.status)">{{ currentCheck.status }}</a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="实际检查日期" v-if="currentCheck.checkDate">
            {{ formatDate(currentCheck.checkDate) }}
          </a-descriptions-item>
        </a-descriptions>
        
        <a-divider />
        
        <div v-if="currentCheck.status !== '待检查'">
          <h3>检查结果</h3>
          <a-table
            :dataSource="currentCheck.checkResults"
            :columns="resultColumns"
            size="small"
            :pagination="false"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'status'">
                <a-tag :color="record.status === '正常' ? 'green' : 'red'">{{ record.status }}</a-tag>
              </template>
            </template>
          </a-table>
          
          <a-divider />
          
          <a-descriptions bordered title="总体评价" size="small" :column="1">
            <a-descriptions-item label="评价内容">{{ currentCheck.overallRemark || '无' }}</a-descriptions-item>
          </a-descriptions>
          
          <div v-if="hasAbnormalItems(currentCheck)" class="maintenance-order">
            <a-alert
              message="发现设备异常"
              description="系统已自动生成维修工单，请及时处理。"
              type="warning"
              showIcon
            />
          </div>
        </div>
        <div v-else>
          <a-empty description="等待检查" />
        </div>
        
        <div style="margin-top: 20px; display: flex; justify-content: flex-end;">
          <a-button @click="detailModalVisible = false">关闭</a-button>
        </div>
      </div>
    </a-modal>

    <!-- 故障上报弹窗 -->
    <a-modal
      v-model:visible="faultReportModalVisible"
      title="故障上报"
      @ok="submitFaultReport"
      @cancel="cancelFaultReport"
      okText="提交"
      cancelText="取消"
    >
      <a-form :model="faultReportFormState" layout="vertical">
        <a-form-item label="故障等级" name="faultLevel" :rules="[{ required: true, message: '请选择故障等级' }]">
          <a-select v-model:value="faultReportFormState.faultLevel" placeholder="选择故障等级">
            <a-select-option value="一般">一般</a-select-option>
            <a-select-option value="严重">严重</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="紧急程度" name="urgency" :rules="[{ required: true, message: '请选择紧急程度' }]">
          <a-select v-model:value="faultReportFormState.urgency" placeholder="选择紧急程度">
            <a-select-option value="常规">常规</a-select-option>
            <a-select-option value="紧急">紧急</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="预计完成时间" name="expectedCompletionTime" :rules="[{ required: true, message: '请选择预计完成时间' }]">
          <a-date-picker v-model:value="faultReportFormState.expectedCompletionTime" style="width: 100%" />
        </a-form-item>
        <a-form-item label="描述" name="description" :rules="[{ required: true, message: '请输入描述' }]">
          <a-textarea v-model:value="faultReportFormState.description" :rows="3" />
        </a-form-item>
        <a-form-item label="维护人员" name="maintenanceStaff" :rules="[{ required: true, message: '请输入维护人员' }]">
          <a-input v-model:value="faultReportFormState.maintenanceStaff" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue';
import { message, Modal } from 'ant-design-vue';
import dayjs from 'dayjs';
import { h } from 'vue';

// 设备列表，实际应用中应该从设备管理中获取
const deviceList = ref([
  { key: '1', name: '监控摄像头' },
  { key: '2', name: '入侵检测器' },
  { key: '3', name: '门禁系统' },
  { key: '4', name: '报警器' }
]);

interface CheckRecord {
  key: string;
  deviceName: string;
  scheduledDate: string | dayjs.Dayjs;
  checkDate?: string | dayjs.Dayjs;
  inspector: string;
  status: string;
  checkItems: string[];
  remark?: string;
  checkResults?: any[];
  overallRemark?: string;
}

interface CheckResult {
  item: string;
  status: string;
  description?: string;
}

// 表格列定义
const columns = [
  {
    title: '设备名称',
    dataIndex: 'deviceName',
  },
  {
    title: '计划检查日期',
    dataIndex: 'scheduledDate',
    customRender: ({ text }: { text: string | dayjs.Dayjs }) => formatDate(text),
    sorter: (a: CheckRecord, b: CheckRecord) => {
      const dateA = dayjs(a.scheduledDate).unix();
      const dateB = dayjs(b.scheduledDate).unix();
      return dateA - dateB;
    },
  },
  {
    title: '负责人员',
    dataIndex: 'inspector',
  },
  {
    title: '状态',
    dataIndex: 'status',
  },
  {
    title: '操作',
    dataIndex: 'action',
  }
];

// 检查结果表格列定义
const resultColumns = [
  {
    title: '检查项目',
    dataIndex: 'item',
  },
  {
    title: '检查结果',
    dataIndex: 'status',
  },
  {
    title: '问题描述',
    dataIndex: 'description',
    ellipsis: true,
  }
];

// 格式化日期
const formatDate = (date: string | dayjs.Dayjs): string => {
  if (!date) return '';
  if (typeof date === 'string') return date;
  return date.format('YYYY-MM-DD');
};

// 获取状态颜色
const getStatusColor = (status: string): string => {
  switch (status) {
    case '待检查':
      return 'blue';
    case '正常':
      return 'green';
    case '异常':
      return 'red';
    default:
      return 'default';
  }
};

// 检查是否有异常项
const hasAbnormalItems = (check: CheckRecord): boolean => {
  if (!check.checkResults) return false;
  return check.checkResults.some(result => result.status === '异常');
};

// 检查记录数据
const checkData = ref<CheckRecord[]>([
  {
    key: '1',
    deviceName: '监控摄像头',
    scheduledDate: '2023-05-10',
    inspector: '张三',
    status: '正常',
    checkItems: ['外观检查', '电气线路', '安全防护装置'],
    checkDate: '2023-05-10',
    checkResults: [
      { item: '外观检查', status: '正常', description: '' },
      { item: '电气线路', status: '正常', description: '' },
      { item: '安全防护装置', status: '正常', description: '' }
    ],
    overallRemark: '设备状态良好，正常运行'
  },
  {
    key: '2',
    deviceName: '入侵检测器',
    scheduledDate: '2023-05-15',
    inspector: '李四',
    status: '异常',
    checkItems: ['外观检查', '电气线路', '软件系统'],
    checkDate: '2023-05-15',
    checkResults: [
      { item: '外观检查', status: '正常', description: '' },
      { item: '电气线路', status: '异常', description: '发现线路老化' },
      { item: '软件系统', status: '正常', description: '' }
    ],
    overallRemark: '电气线路需要更换'
  },
  {
    key: '3',
    deviceName: '门禁系统',
    scheduledDate: dayjs().add(3, 'day'),
    inspector: '王五',
    status: '待检查',
    checkItems: ['外观检查', '电气线路', '安全防护装置', '软件系统'],
    remark: '重点检查门禁卡读取功能'
  }
]);

// 表单状态
interface FormState {
  deviceName: string;
  scheduledDate: dayjs.Dayjs | null;
  checkItems: string[];
  inspector: string;
  remark: string;
}

const formState = reactive<FormState>({
  deviceName: '',
  scheduledDate: null,
  checkItems: ['外观检查', '电气线路', '安全防护装置'],
  inspector: '',
  remark: '',
});

// 检查表单状态
interface CheckFormState {
  results: Array<{
    status: string;
    description: string;
  }>;
  checkDate: dayjs.Dayjs | null;
  overallRemark: string;
}

const checkFormState = reactive<CheckFormState>({
  results: [],
  checkDate: dayjs(),
  overallRemark: '',
});

// 模态框可见性
const addModalVisible = ref(false);
const checkModalVisible = ref(false);
const detailModalVisible = ref(false);
const currentCheck = ref<CheckRecord | null>(null);

// 显示添加对话框
const showAddModal = () => {
  resetForm();
  addModalVisible.value = true;
};

// 处理添加检查计划
const handleAddOk = () => {
  if (!formState.deviceName || !formState.scheduledDate || !formState.inspector) {
    message.error('请填写必填项');
    return;
  }

  if (formState.checkItems.length === 0) {
    message.error('请至少选择一个检查项目');
    return;
  }

  const newCheck: CheckRecord = {
    key: Date.now().toString(),
    deviceName: formState.deviceName,
    scheduledDate: formState.scheduledDate,
    inspector: formState.inspector,
    status: '待检查',
    checkItems: formState.checkItems,
    remark: formState.remark
  };

  checkData.value.push(newCheck);
  message.success('检查计划创建成功');
  addModalVisible.value = false;
  resetForm();
};

const handleAddCancel = () => {
  addModalVisible.value = false;
  resetForm();
};

// 显示检查结果录入对话框
const showCheckModal = (record: CheckRecord) => {
  currentCheck.value = record;
  
  // 根据检查项目初始化结果表单
  checkFormState.results = record.checkItems.map(item => ({
    status: '正常',
    description: ''
  }));
  
  checkFormState.checkDate = dayjs();
  checkFormState.overallRemark = '';
  
  checkModalVisible.value = true;
};

// 处理检查结果提交
const handleCheckOk = () => {
  if (!currentCheck.value || !checkFormState.checkDate) {
    message.error('数据异常');
    return;
  }

  // 检查是否有异常项
  const hasAbnormal = checkFormState.results.some(result => result.status === '异常');
  
  // 更新检查记录
  const index = checkData.value.findIndex(item => item.key === currentCheck.value?.key);
  if (index !== -1) {
    const checkResults = currentCheck.value.checkItems.map((item, idx) => ({
      item,
      status: checkFormState.results[idx].status,
      description: checkFormState.results[idx].description || ''
    }));
    
    checkData.value[index] = {
      ...checkData.value[index],
      status: hasAbnormal ? '异常' : '正常',
      checkDate: checkFormState.checkDate,
      checkResults,
      overallRemark: checkFormState.overallRemark
    };
    
    message.success('检查结果提交成功');
    
    if (hasAbnormal) {
      // 显示故障上报确认
      showFaultReportModal(checkData.value[index]);
    }
  }
  
  checkModalVisible.value = false;
};

const handleCheckCancel = () => {
  checkModalVisible.value = false;
};

// 显示删除确认
const showDeleteConfirm = (record: CheckRecord) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除设备"${record.deviceName}"的检查记录吗？此操作无法撤销。`,
    okText: '确认',
    okType: 'danger',
    cancelText: '取消',
    onOk() {
      deleteCheck(record.key);
    },
  });
};

// 删除检查记录
const deleteCheck = (key: string) => {
  checkData.value = checkData.value.filter(item => item.key !== key);
  message.success('检查记录已删除');
};

// 查看检查详情
const viewCheckDetails = (record: CheckRecord) => {
  currentCheck.value = record;
  detailModalVisible.value = true;
};

// 重置表单
const resetForm = () => {
  formState.deviceName = '';
  formState.scheduledDate = null;
  formState.checkItems = ['外观检查', '电气线路', '安全防护装置'];
  formState.inspector = '';
  formState.remark = '';
};

// 故障上报相关
interface FaultReportFormState {
  faultLevel: string;
  urgency: string;
  expectedCompletionTime: dayjs.Dayjs | null;
  description: string;
  maintenanceStaff: string;
}

const faultReportModalVisible = ref(false);
const faultReportFormState = reactive<FaultReportFormState>({
  faultLevel: '一般',
  urgency: '常规',
  expectedCompletionTime: null,
  description: '',
  maintenanceStaff: '',
});

// 显示故障上报对话框
const showFaultReportModal = (record: CheckRecord) => {
  currentCheck.value = record;
  
  // 初始化故障描述
  let description = '设备检查发现以下异常项：\n';
  record.checkResults?.forEach(item => {
    if (item.status === '异常') {
      description += `- ${item.item}: ${item.description || '无描述'}\n`;
    }
  });
  
  faultReportFormState.description = description;
  faultReportFormState.expectedCompletionTime = dayjs().add(2, 'day');
  
  faultReportModalVisible.value = true;
};

// 提交故障上报
const submitFaultReport = () => {
  if (!currentCheck.value || !faultReportFormState.maintenanceStaff || !faultReportFormState.expectedCompletionTime) {
    message.error('请填写所有必填项');
    return;
  }
  
  // 生成维修工单数据
  const faultReport = {
    deviceName: currentCheck.value.deviceName,
    maintenanceType: '故障维修',
    startDate: dayjs(),
    estimatedEndDate: faultReportFormState.expectedCompletionTime,
    maintenanceStaff: faultReportFormState.maintenanceStaff,
    description: faultReportFormState.description,
    status: '进行中',
    faultLevel: faultReportFormState.faultLevel,
    urgency: faultReportFormState.urgency,
    fromCheck: currentCheck.value.key,
  };
  
  // 这里应该调用接口或者将数据传递给设备维修记录组件
  // 由于组件间通信限制，这里我们暂时使用localStorage模拟
  const faultReports = JSON.parse(localStorage.getItem('faultReports') || '[]');
  faultReports.push({
    key: Date.now().toString(),
    ...faultReport
  });
  localStorage.setItem('faultReports', JSON.stringify(faultReports));
  
  message.success('故障上报成功，已生成维修工单');
  faultReportModalVisible.value = false;
};

// 取消故障上报
const cancelFaultReport = () => {
  faultReportModalVisible.value = false;
};
</script>

<style scoped>
.device-check-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.action-buttons .ant-btn {
  margin-right: 8px;
}

.maintenance-order {
  margin-top: 16px;
}
</style>
