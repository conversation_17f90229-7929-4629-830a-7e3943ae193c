<template>
  <div class="chat-container">
    <!-- 聊天头部 -->
    <div class="chat-header">
      <button class="back-btn" @click="goBack">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <path d="M15 18L9 12L15 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </button>
    </div>

    <!-- 聊天消息区域 -->
    <div class="chat-messages" ref="messagesContainer">
      <div
        v-for="message in messages"
        :key="message.id"
        :class="['message-item', { 'message-sent': message.isSent, 'message-received': !message.isSent }]"
      >
        <!-- 接收消息（左侧） -->
        <div v-if="!message.isSent" class="message-wrapper">
          <img :src="message.avatar" :alt="message.senderName" class="message-avatar" />
          <div class="message-content-wrapper">
            <div class="message-sender-name">{{ message.senderName }}</div>
            <div class="message-bubble message-bubble-received">
              <div class="message-text">{{ message.text }}</div>
            </div>
            <div class="message-time">{{ formatMessageTime(message.timestamp) }}</div>
          </div>
        </div>

        <!-- 发送消息（右侧） -->
        <div v-else class="message-wrapper">
          <div class="message-content-wrapper">
            <div class="message-sender-name">{{ message.senderName }}</div>
            <div class="message-bubble message-bubble-sent">
              <div class="message-text">{{ message.text }}</div>
            </div>
            <div class="message-time">{{ formatMessageTime(message.timestamp) }}</div>
          </div>
          <img :src="message.avatar" :alt="message.senderName" class="message-avatar" />
        </div>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="chat-input-area">
      <div class="input-toolbar">
        <button class="toolbar-btn" @click="showEmojiPicker = !showEmojiPicker">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
            <path d="M8 14s1.5 2 4 2 4-2 4-2" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            <line x1="9" y1="9" x2="9.01" y2="9" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            <line x1="15" y1="9" x2="15.01" y2="9" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
          </svg>
        </button>
        <button class="toolbar-btn voice-btn" @click="toggleVoiceInput">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z" stroke="currentColor" stroke-width="2"/>
            <path d="M19 10v2a7 7 0 0 1-14 0v-2" stroke="currentColor" stroke-width="2"/>
            <line x1="12" y1="19" x2="12" y2="23" stroke="currentColor" stroke-width="2"/>
            <line x1="8" y1="23" x2="16" y2="23" stroke="currentColor" stroke-width="2"/>
          </svg>
        </button>
      </div>

      <div class="input-container">
        <textarea
          v-model="inputMessage"
          @keydown="handleKeyDown"
          @input="adjustTextareaHeight"
          ref="messageInput"
          placeholder="输入消息..."
          class="message-input"
          rows="1"
        ></textarea>
        <button
          class="send-btn"
          @click="sendMessage"
          :disabled="!inputMessage.trim()"
          :class="{ active: inputMessage.trim() }"
        >
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
            <line x1="22" y1="2" x2="11" y2="13" stroke="currentColor" stroke-width="2"/>
            <polygon points="22,2 15,22 11,13 2,9 22,2" fill="currentColor"/>
          </svg>
        </button>
      </div>

      <!-- 表情选择器 -->
      <div v-if="showEmojiPicker" class="emoji-picker">
        <div class="emoji-grid">
          <button
            v-for="emoji in commonEmojis"
            :key="emoji"
            @click="insertEmoji(emoji)"
            class="emoji-btn"
          >
            {{ emoji }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue';
import { useRouter } from 'vue-router';
import { getUserInfo } from '../utils/auth.js';

const router = useRouter();

// 当前用户信息
const currentUser = ref(getUserInfo());

// 聊天对象信息
const chatUser = reactive({
  id: 2,
  name: '张老师',
  avatar: 'src/assets/images/default-avatar.png',
  status: '在线'
});

// 消息列表
const messages = ref([
  {
    id: 1,
    text: '你好，请问有什么可以帮助您的吗？',
    timestamp: Date.now() - 3600000, // 1小时前
    isSent: false,
    senderName: '张老师',
    avatar: 'src/assets/images/default-avatar.png'
  },
  {
    id: 2,
    text: '我想了解一下实训室的使用情况',
    timestamp: Date.now() - 3500000,
    isSent: true,
    senderName: currentUser.value?.name || '我',
    avatar: currentUser.value?.avatar || 'src/assets/images/default-avatar.png'
  },
  {
    id: 3,
    text: '好的，我来为您查看一下当前的使用情况。根据系统显示，目前实训室A有15人在使用，实训室B有8人在使用。',
    timestamp: Date.now() - 3400000,
    isSent: false,
    senderName: '张老师',
    avatar: 'src/assets/images/default-avatar.png'
  },
  {
    id: 4,
    text: '谢谢！还想问一下设备检查的情况',
    timestamp: Date.now() - 3300000,
    isSent: true,
    senderName: currentUser.value?.name || '我',
    avatar: currentUser.value?.avatar || 'src/assets/images/default-avatar.png'
  },
  {
    id: 5,
    text: '设备检查方面，今天已完成12台设备的检查，其中11台正常，1台需要维修。详细报告我稍后发给您。',
    timestamp: Date.now() - 86400000, // 昨天
    isSent: false,
    senderName: '张老师',
    avatar: 'src/assets/images/default-avatar.png'
  }
]);

// 输入相关
const inputMessage = ref('');
const showEmojiPicker = ref(false);
const messagesContainer = ref(null);
const messageInput = ref(null);

// 常用表情
const commonEmojis = [
  '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣',
  '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰',
  '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜',
  '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳', '😏',
  '👍', '👎', '👌', '✌️', '🤞', '🤟', '🤘', '🤙',
  '👏', '🙌', '👐', '🤲', '🤝', '🙏', '✍️', '💪'
];

// 返回上一页
const goBack = () => {
  router.go(-1);
};

// 格式化消息时间
const formatMessageTime = (timestamp) => {
  const now = new Date();
  const messageDate = new Date(timestamp);
  const diffTime = now.getTime() - messageDate.getTime();
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays === 0) {
    // 今天
    return messageDate.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  } else if (diffDays === 1) {
    // 昨天
    return '昨天 ' + messageDate.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  } else {
    // 更早的日期
    return messageDate.toLocaleDateString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    }).replace(/\//g, '/');
  }
};

// 发送消息
const sendMessage = () => {
  if (!inputMessage.value.trim()) return;

  const newMessage = {
    id: Date.now(),
    text: inputMessage.value.trim(),
    timestamp: Date.now(),
    isSent: true,
    senderName: currentUser.value?.name || '我',
    avatar: currentUser.value?.avatar || 'src/assets/images/default-avatar.png'
  };

  messages.value.push(newMessage);
  inputMessage.value = '';
  showEmojiPicker.value = false;

  // 滚动到底部
  nextTick(() => {
    scrollToBottom();
  });

  // 模拟对方回复
  setTimeout(() => {
    simulateReply();
  }, 1000 + Math.random() * 2000);
};

// 模拟对方回复
const simulateReply = () => {
  const replies = [
    '收到，我来处理一下。',
    '好的，明白了。',
    '这个问题我需要查看一下，稍等。',
    '没问题，我会及时跟进的。',
    '谢谢您的反馈！',
    '我会尽快给您答复。'
  ];

  const randomReply = replies[Math.floor(Math.random() * replies.length)];

  const replyMessage = {
    id: Date.now(),
    text: randomReply,
    timestamp: Date.now(),
    isSent: false,
    senderName: chatUser.name,
    avatar: chatUser.avatar
  };

  messages.value.push(replyMessage);

  nextTick(() => {
    scrollToBottom();
  });
};

// 滚动到底部
const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
  }
};

// 处理键盘事件
const handleKeyDown = (event) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault();
    sendMessage();
  }
};

// 自动调整输入框高度
const adjustTextareaHeight = () => {
  const textarea = messageInput.value;
  if (textarea) {
    textarea.style.height = 'auto';
    textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
  }
};

// 插入表情
const insertEmoji = (emoji) => {
  inputMessage.value += emoji;
  showEmojiPicker.value = false;
  messageInput.value?.focus();
};

// 切换语音输入
const toggleVoiceInput = () => {
  // 这里可以实现语音输入功能
  console.log('语音输入功能待实现');
};

// 组件挂载后滚动到底部
onMounted(() => {
  nextTick(() => {
    scrollToBottom();
  });
});
</script>

<style scoped>
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
}

.back-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
  margin-right: 12px;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 消息区域 */
.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background-color: #f5f5f5;
  scroll-behavior: smooth;
}

.message-item {
  margin-bottom: 16px;
}

.message-wrapper {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  max-width: 80%;
}

.message-sent .message-wrapper {
  margin-left: auto;
  flex-direction: row-reverse;
}

.message-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  flex-shrink: 0;
  border: 1px solid #e0e0e0;
}

.message-content-wrapper {
  display: flex;
  flex-direction: column;
  gap: 4px;
  max-width: calc(100% - 44px);
}

.message-sent .message-content-wrapper {
  align-items: flex-end;
}

.message-sender-name {
  font-size: 12px;
  color: #666;
  padding: 0 12px;
  line-height: 1;
}

.message-bubble {
  padding: 10px 16px;
  border-radius: 18px;
  word-wrap: break-word;
  position: relative;
  max-width: 100%;
}

.message-bubble-received {
  background-color: white;
  border: 1px solid #e0e0e0;
  border-bottom-left-radius: 4px;
}

.message-bubble-sent {
  background: linear-gradient(135deg, #07c160 0%, #06ad56 100%);
  color: white;
  border-bottom-right-radius: 4px;
}

.message-text {
  font-size: 14px;
  line-height: 1.4;
  margin: 0;
}

.message-time {
  font-size: 11px;
  color: #999;
  padding: 0 12px;
  line-height: 1;
}

/* 输入区域 */
.chat-input-area {
  background-color: white;
  border-top: 1px solid #e0e0e0;
  padding: 12px 16px;
  position: relative;
}

.input-toolbar {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.toolbar-btn {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.toolbar-btn:hover {
  background-color: #f0f0f0;
  color: #333;
}

.voice-btn:hover {
  color: #07c160;
}

.input-container {
  display: flex;
  align-items: flex-end;
  gap: 8px;
  background-color: #f8f8f8;
  border-radius: 20px;
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
}

.message-input {
  flex: 1;
  border: none;
  background: none;
  outline: none;
  resize: none;
  font-size: 14px;
  line-height: 1.4;
  min-height: 20px;
  max-height: 120px;
  font-family: inherit;
  color: #333;
}

.message-input::placeholder {
  color: #999;
}

.send-btn {
  background: #ccc;
  border: none;
  color: white;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  width: 36px;
  height: 36px;
  flex-shrink: 0;
}

.send-btn:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.send-btn.active {
  background: linear-gradient(135deg, #07c160 0%, #06ad56 100%);
  cursor: pointer;
  opacity: 1;
}

.send-btn.active:hover {
  background: linear-gradient(135deg, #06ad56 0%, #059c4f 100%);
  transform: scale(1.05);
}

/* 表情选择器 */
.emoji-picker {
  position: absolute;
  bottom: 100%;
  left: 16px;
  right: 16px;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  padding: 16px;
  margin-bottom: 8px;
  z-index: 100;
}

.emoji-grid {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.emoji-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.emoji-btn:hover {
  background-color: #f0f0f0;
}

/* 滚动条样式 */
.chat-messages::-webkit-scrollbar {
  width: 4px;
}

.chat-messages::-webkit-scrollbar-track {
  background: transparent;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 2px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: #999;
}

.emoji-grid::-webkit-scrollbar {
  width: 4px;
}

.emoji-grid::-webkit-scrollbar-track {
  background: #f0f0f0;
}

.emoji-grid::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 2px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-container {
    height: 100vh;
  }

  .message-wrapper {
    max-width: 90%;
  }



  .message-text {
    font-size: 13px;
  }

  .emoji-grid {
    grid-template-columns: repeat(6, 1fr);
  }
}

/* 动画效果 */
.message-item {
  animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 输入框聚焦效果 */
.input-container:focus-within {
  border-color: #07c160;
  box-shadow: 0 0 0 2px rgba(7, 193, 96, 0.1);
}
</style>