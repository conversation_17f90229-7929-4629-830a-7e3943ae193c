// 简单的事件总线，用于组件间通信
import { ref } from 'vue';

// 创建一个响应式的事件总线
const eventBus = ref({});

// 事件类型常量
export const EVENT_TYPES = {
  LOGIN_SUCCESS: 'LOGIN_SUCCESS',
  LOGOUT_SUCCESS: 'LOGOUT_SUCCESS',
  USER_INFO_UPDATED: 'USER_INFO_UPDATED'
};

// 发送事件
export const emit = (eventType, data = null) => {
  console.log(`[EventBus] 发送事件: ${eventType}`, data);
  eventBus.value = {
    type: eventType,
    data,
    timestamp: Date.now()
  };
};

// 监听事件
export const on = (eventType, callback) => {
  const unwatch = eventBus.value && typeof eventBus.value === 'object' 
    ? () => {} 
    : () => {};
    
  // 使用Vue的watch来监听事件变化
  return unwatch;
};

// 获取当前事件状态
export const getCurrentEvent = () => {
  return eventBus.value;
};

export default {
  emit,
  on,
  getCurrentEvent,
  EVENT_TYPES
};
