<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import PerInfo from './Per_Info.vue'
import DeviceInfo from './Device_Info.vue'
import DeviceCheck from './Device_check.vue'
import DeviceLog from './Device_log.vue'
import Attendance from './Attendance.vue'

// 路由实例
const router = useRouter()

// 当前激活的导航项
const activeTab = ref('per')
const activeDeviceTab = ref('info') // 设备相关页面的子选项卡

// 切换导航项
const switchTab = (tab) => {
  activeTab.value = tab
}

// 切换设备相关子选项卡
const switchDeviceTab = (tab) => {
  activeDeviceTab.value = tab
}

// 返回首页
const goHome = () => {
  router.push('/home')
}
</script>

<template>
  <div class="manage-container">
    <!-- 导航栏 -->
    <div class="nav-bar">
      <!-- 返回按钮 -->
      <button class="back-btn" @click="goHome">
        ← 返回首页
      </button>

      <!-- 导航按钮 -->
      <div class="nav-buttons">
        <button
          :class="['nav-btn', { active: activeTab === 'per' }]"
          @click="switchTab('per')"
        >
          人员信息
        </button>
        <button
          :class="['nav-btn', { active: activeTab === 'attendance' }]"
          @click="switchTab('attendance')"
        >
          考勤管理
        </button>
        <button
          :class="['nav-btn', { active: activeTab === 'device' }]"
          @click="switchTab('device')"
        >
          设备管理
        </button>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-area">
      <!-- 人员信息 -->
      <PerInfo v-if="activeTab === 'per'" />
      
      <!-- 考勤管理 -->
      <Attendance v-if="activeTab === 'attendance'" />
      
      <!-- 设备相关 -->
      <div v-if="activeTab === 'device'" class="device-section">
        <!-- 设备子选项卡 -->
        <div class="device-tabs">
          <button 
            :class="['device-tab', { active: activeDeviceTab === 'info' }]"
            @click="switchDeviceTab('info')"
          >
            设备信息
          </button>
          <button 
            :class="['device-tab', { active: activeDeviceTab === 'check' }]"
            @click="switchDeviceTab('check')"
          >
            安全检查
          </button>
          <button 
            :class="['device-tab', { active: activeDeviceTab === 'log' }]"
            @click="switchDeviceTab('log')"
          >
            维修记录
          </button>
        </div>
        
        <!-- 设备子页面 -->
        <div class="device-content">
          <DeviceInfo v-if="activeDeviceTab === 'info'" />
          <DeviceCheck v-if="activeDeviceTab === 'check'" />
          <DeviceLog v-if="activeDeviceTab === 'log'" />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.manage-container {
  width: 100%;
  height: 100%;
}

.nav-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e8e8e8;
  background-color: #fff;
  padding: 0 16px;
}

.back-btn {
  padding: 8px 16px;
  border: 1px solid #d9d9d9;
  background-color: #fff;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.back-btn:hover {
  color: #1890ff;
  border-color: #1890ff;
}

.nav-buttons {
  display: flex;
}

.nav-btn {
  padding: 12px 24px;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  border-bottom: 2px solid transparent;
  transition: all 0.3s ease;
}

.nav-btn:hover {
  color: #1890ff;
}

.nav-btn.active {
  color: #1890ff;
  border-bottom-color: #1890ff;
}

.content-area {
  padding: 16px;
  height: calc(100% - 60px);
  overflow: auto;
}

.device-section {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.device-tabs {
  display: flex;
  border-bottom: 1px solid #e8e8e8;
  margin-bottom: 16px;
}

.device-tab {
  padding: 8px 16px;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  border-bottom: 2px solid transparent;
  transition: all 0.3s ease;
}

.device-tab:hover {
  color: #1890ff;
}

.device-tab.active {
  color: #1890ff;
  border-bottom-color: #1890ff;
}

.device-content {
  flex: 1;
}
</style>
