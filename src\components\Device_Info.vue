<template>
  <div class="device-info-container">
    <div class="header">
      <h2>设备信息管理</h2>
      <a-button type="primary" @click="showAddModal">添加设备</a-button>
    </div>
    
    <a-table :columns="columns" :data-source="deviceData" :row-key="record => record.key">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <div class="action-buttons">
            <a-button type="primary" size="small" @click="showEditModal(record)">编辑</a-button>
            <a-button type="primary" danger size="small" @click="showDeleteConfirm(record)">删除</a-button>
            <a-button size="small" @click="viewDeviceDetails(record)">详情</a-button>
          </div>
        </template>
      </template>
    </a-table>

    <!-- 添加设备弹窗 -->
    <a-modal
      v-model:visible="addModalVisible"
      title="添加设备"
      @ok="handleAddOk"
      @cancel="handleAddCancel"
      okText="确认"
      cancelText="取消"
    >
      <a-form :model="formState" layout="vertical">
        <a-form-item label="设备名称" name="name" :rules="[{ required: true, message: '请输入设备名称' }]">
          <a-input v-model:value="formState.name" />
        </a-form-item>
        <a-form-item label="设备型号" name="model" :rules="[{ required: true, message: '请输入设备型号' }]">
          <a-input v-model:value="formState.model" />
        </a-form-item>
        <a-form-item label="生产厂家" name="manufacturer">
          <a-input v-model:value="formState.manufacturer" />
        </a-form-item>
        <a-form-item label="购买日期" name="purchaseDate">
          <a-date-picker v-model:value="formState.purchaseDate" style="width: 100%" />
        </a-form-item>
        <a-form-item label="使用说明书" name="manual">
          <a-textarea v-model:value="formState.manual" :rows="3" />
        </a-form-item>
        <a-form-item label="维修联系电话" name="phone">
          <a-input v-model:value="formState.phone" />
        </a-form-item>
        <a-form-item label="设备状态" name="status">
          <a-select v-model:value="formState.status">
            <a-select-option value="正常">正常</a-select-option>
            <a-select-option value="维修中">维修中</a-select-option>
            <a-select-option value="待检查">待检查</a-select-option>
            <a-select-option value="报废">报废</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 编辑设备弹窗 -->
    <a-modal
      v-model:visible="editModalVisible"
      title="编辑设备信息"
      @ok="handleEditOk"
      @cancel="handleEditCancel"
      okText="确认"
      cancelText="取消"
    >
      <a-form :model="formState" layout="vertical">
        <a-form-item label="设备名称" name="name" :rules="[{ required: true, message: '请输入设备名称' }]">
          <a-input v-model:value="formState.name" />
        </a-form-item>
        <a-form-item label="设备型号" name="model" :rules="[{ required: true, message: '请输入设备型号' }]">
          <a-input v-model:value="formState.model" />
        </a-form-item>
        <a-form-item label="生产厂家" name="manufacturer">
          <a-input v-model:value="formState.manufacturer" />
        </a-form-item>
        <a-form-item label="购买日期" name="purchaseDate">
          <a-date-picker v-model:value="formState.purchaseDate" style="width: 100%" />
        </a-form-item>
        <a-form-item label="使用说明书" name="manual">
          <a-textarea v-model:value="formState.manual" :rows="3" />
        </a-form-item>
        <a-form-item label="维修联系电话" name="phone">
          <a-input v-model:value="formState.phone" />
        </a-form-item>
        <a-form-item label="设备状态" name="status">
          <a-select v-model:value="formState.status">
            <a-select-option value="正常">正常</a-select-option>
            <a-select-option value="维修中">维修中</a-select-option>
            <a-select-option value="待检查">待检查</a-select-option>
            <a-select-option value="报废">报废</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 设备详情弹窗 -->
    <a-modal
      v-model:visible="detailModalVisible"
      title="设备详情"
      :footer="null"
      width="700px"
    >
      <div v-if="currentDevice">
        <a-descriptions bordered>
          <a-descriptions-item label="设备名称" span="3">{{ currentDevice.name }}</a-descriptions-item>
          <a-descriptions-item label="设备型号" span="2">{{ currentDevice.model }}</a-descriptions-item>
          <a-descriptions-item label="生产厂家">{{ currentDevice.manufacturer }}</a-descriptions-item>
          <a-descriptions-item label="购买日期">{{ formatDate(currentDevice.purchaseDate) }}</a-descriptions-item>
          <a-descriptions-item label="设备状态" span="2">
            <a-tag :color="getStatusColor(currentDevice.status)">{{ currentDevice.status }}</a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="维修联系电话" span="3">{{ currentDevice.phone }}</a-descriptions-item>
          <a-descriptions-item label="使用说明书" span="3">{{ currentDevice.manual }}</a-descriptions-item>
        </a-descriptions>
        
        <div style="margin-top: 20px; display: flex; justify-content: flex-end;">
          <a-button @click="detailModalVisible = false">关闭</a-button>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue';
import { message, Modal } from 'ant-design-vue';
import dayjs from 'dayjs';
import type { TableProps } from 'ant-design-vue';

interface DeviceRecord {
  key: string;
  name: string;
  model: string;
  manufacturer: string;
  purchaseDate: string | dayjs.Dayjs | null;
  status: string;
  manual: string;
  phone: string;
}

// 表格列定义
const columns = [
  {
    title: '设备名称',
    dataIndex: 'name',
  },
  {
    title: '型号',
    dataIndex: 'model',
    sorter: (a: DeviceRecord, b: DeviceRecord) => a.model.localeCompare(b.model),
  },
  {
    title: '生产厂家',
    dataIndex: 'manufacturer',
    width: '40%',
  },
  {
    title: '购买日期',
    dataIndex: 'purchaseDate',
    customRender: ({ text }: { text: string | dayjs.Dayjs }) => formatDate(text),
    sorter: (a: DeviceRecord, b: DeviceRecord) => {
      const dateA = a.purchaseDate ? dayjs(a.purchaseDate).unix() : 0;
      const dateB = b.purchaseDate ? dayjs(b.purchaseDate).unix() : 0;
      return dateA - dateB;
    },
  },
  {
    title: '状态',
    dataIndex: 'status',
    customRender: ({ text }: { text: string }) => {
      const color = getStatusColor(text);
      return h('span', {}, [
        h('a-tag', { color }, text)
      ]);
    },
  },
  {
    title: '维修电话',
    dataIndex: 'phone',
  },
  {
    title: '操作',
    dataIndex: 'action',
  },
];

// 获取状态对应的颜色
const getStatusColor = (status: string): string => {
  switch (status) {
    case '正常':
      return 'green';
    case '维修中':
      return 'orange';
    case '待检查':
      return 'blue';
    case '报废':
      return 'red';
    default:
      return 'default';
  }
};

// 格式化日期
const formatDate = (date: string | dayjs.Dayjs | null): string => {
  if (!date) return '';
  if (typeof date === 'string') return date;
  return date.format('YYYY-MM-DD');
};

// 设备数据
const deviceData = ref<DeviceRecord[]>([
  {
    key: '1',
    name: '监控摄像头',
    model: 'HIKVISION-DS2CD3T45',
    manufacturer: '海康威视',
    purchaseDate: '2023-01-15',
    status: '正常',
    manual: '使用前请确保电源正常连接，网络正常连接...',
    phone: '************',
  },
  {
    key: '2',
    name: '入侵检测器',
    model: 'PIR-M10',
    manufacturer: '博世安防',
    purchaseDate: '2023-03-20',
    status: '待检查',
    manual: '安装在房门或窗户上，定期检查电池电量...',
    phone: '************',
  },
]);

// 表单状态
interface FormState {
  key: string;
  name: string;
  model: string;
  manufacturer: string;
  purchaseDate: dayjs.Dayjs | null;
  status: string;
  manual: string;
  phone: string;
}

const formState = reactive<FormState>({
  key: '',
  name: '',
  model: '',
  manufacturer: '',
  purchaseDate: null,
  status: '正常',
  manual: '',
  phone: '',
});

// 模态框可见性
const addModalVisible = ref(false);
const editModalVisible = ref(false);
const detailModalVisible = ref(false);
const currentDevice = ref<DeviceRecord | null>(null);

// 显示添加对话框
const showAddModal = () => {
  resetForm();
  addModalVisible.value = true;
};

// 处理添加设备
const handleAddOk = () => {
  if (!formState.name || !formState.model) {
    message.error('设备名称和型号为必填项！');
    return;
  }

  const newDevice: DeviceRecord = {
    key: Date.now().toString(),
    name: formState.name,
    model: formState.model,
    manufacturer: formState.manufacturer,
    purchaseDate: formState.purchaseDate,
    status: formState.status,
    manual: formState.manual,
    phone: formState.phone,
  };

  deviceData.value.push(newDevice);
  message.success('设备添加成功');
  addModalVisible.value = false;
  resetForm();
};

const handleAddCancel = () => {
  addModalVisible.value = false;
  resetForm();
};

// 显示编辑对话框
const showEditModal = (record: DeviceRecord) => {
  formState.key = record.key;
  formState.name = record.name;
  formState.model = record.model;
  formState.manufacturer = record.manufacturer;
  formState.status = record.status;
  formState.manual = record.manual;
  formState.phone = record.phone;
  
  // 处理日期对象
  if (record.purchaseDate && typeof record.purchaseDate === 'string') {
    formState.purchaseDate = dayjs(record.purchaseDate);
  } else {
    formState.purchaseDate = record.purchaseDate as dayjs.Dayjs | null;
  }
  
  editModalVisible.value = true;
};

// 处理设备编辑
const handleEditOk = () => {
  if (!formState.name || !formState.model) {
    message.error('设备名称和型号为必填项！');
    return;
  }

  const index = deviceData.value.findIndex(item => item.key === formState.key);
  if (index !== -1) {
    deviceData.value[index] = {
      ...deviceData.value[index],
      name: formState.name,
      model: formState.model,
      manufacturer: formState.manufacturer,
      purchaseDate: formState.purchaseDate,
      status: formState.status,
      manual: formState.manual,
      phone: formState.phone,
    };
    message.success('设备信息更新成功');
  }
  
  editModalVisible.value = false;
  resetForm();
};

const handleEditCancel = () => {
  editModalVisible.value = false;
  resetForm();
};

// 显示删除确认
const showDeleteConfirm = (record: DeviceRecord) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除设备 "${record.name}" 吗？此操作无法撤销。`,
    okText: '确认',
    okType: 'danger',
    cancelText: '取消',
    onOk() {
      deleteDevice(record.key);
    },
  });
};

// 删除设备
const deleteDevice = (key: string) => {
  deviceData.value = deviceData.value.filter(item => item.key !== key);
  message.success('设备已删除');
};

// 查看设备详情
const viewDeviceDetails = (record: DeviceRecord) => {
  currentDevice.value = record;
  detailModalVisible.value = true;
};

// 重置表单
const resetForm = () => {
  formState.key = '';
  formState.name = '';
  formState.model = '';
  formState.manufacturer = '';
  formState.purchaseDate = null;
  formState.status = '正常';
  formState.manual = '';
  formState.phone = '';
};

import { h } from 'vue';
</script>

<style scoped>
.device-info-container {
    padding: 20px;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.action-buttons .ant-btn {
    margin-right: 8px;
}
</style>
