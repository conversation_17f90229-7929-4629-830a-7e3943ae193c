<template>
    <div class="attendance-container">
        <div class="attendance-header">
            <h2>人员考勤管理</h2>
            <div class="header-actions">
                <a-radio-group v-model:value="viewMode" button-style="solid">
                    <a-radio-button value="record">考勤记录</a-radio-button>
                    <a-radio-button value="check">人脸识别打卡</a-radio-button>
                </a-radio-group>
            </div>
        </div>

        <!-- 考勤记录视图 -->
        <div v-if="viewMode === 'record'" class="attendance-record">
            <div class="search-bar">
                <a-row :gutter="16">
                    <a-col :span="6">
                        <a-select
                            v-model:value="filters.department"
                            placeholder="选择班级/部门"
                            style="width: 100%">
                            <a-select-option value="">全部</a-select-option>
                            <a-select-option value="计算机科学与技术系">计算机科学与技术系</a-select-option>
                            <a-select-option value="计算机1班">计算机1班</a-select-option>
                            <a-select-option value="计算机2班">计算机2班</a-select-option>
                            <a-select-option value="设备部">设备部</a-select-option>
                        </a-select>
                    </a-col>
                    <a-col :span="6">
                        <a-select
                            v-model:value="filters.role"
                            placeholder="选择角色"
                            style="width: 100%">
                            <a-select-option value="">全部</a-select-option>
                            <a-select-option value="teacher">教师</a-select-option>
                            <a-select-option value="student">学生</a-select-option>
                            <a-select-option value="other">其他人员</a-select-option>
                        </a-select>
                    </a-col>
                    <a-col :span="6">
                        <a-range-picker 
                            v-model:value="dateRange"
                            format="YYYY-MM-DD" 
                            style="width: 100%"
                            @change="onDateRangeChange" />
                    </a-col>
                    <a-col :span="6">
                        <a-input-search 
                            v-model:value="filters.keyword" 
                            placeholder="搜索姓名/学号/ID" 
                            @search="onSearch" />
                    </a-col>
                </a-row>
            </div>

            <a-table 
                :columns="columns" 
                :data-source="filteredAttendanceData"
                :pagination="{ pageSize: 10 }">
                <template #headerCell="{ column }">
                    <template v-if="column.key === 'name'">
                        <span>
                            <user-outlined />
                            {{ column.title }}
                        </span>
                    </template>
                </template>
                
                <template #bodyCell="{ column, record }">
                    <template v-if="column.key === 'attendance_status'">
                        <a-tag :color="record.attendance_status === '出勤' ? 'green' : record.attendance_status === '缺勤' ? 'red' : 'orange'">
                            {{ record.attendance_status }}
                        </a-tag>
                    </template>
                    <template v-else-if="column.key === 'action'">
                        <a-button type="link" @click="viewAttendanceDetails(record)">查看详情</a-button>
                    </template>
                </template>
            </a-table>
        </div>

        <!-- 人脸识别打卡视图 -->
        <div v-else-if="viewMode === 'check'" class="attendance-check">
            <div class="facial-recognition-container">
                <div class="camera-view">
                    <div v-if="!isCameraActive" class="camera-placeholder">
                        <video-camera-outlined />
                        <p>点击启动摄像头进行打卡</p>
                        <a-button type="primary" @click="startCamera">启动摄像头</a-button>
                    </div>
                    <div v-else class="camera-active">
                        <video ref="videoElement" autoplay class="camera-feed"></video>
                        <canvas ref="canvasElement" class="face-canvas"></canvas>
                    </div>
                </div>

                <div class="recognition-results">
                    <div v-if="recognizedPerson" class="recognition-success">
                        <check-circle-outlined />
                        <h3>识别成功</h3>
                        <div class="person-info">
                            <p><strong>姓名：</strong>{{ recognizedPerson.name }}</p>
                            <p><strong>ID：</strong>{{ recognizedPerson.id }}</p>
                            <p><strong>班级/部门：</strong>{{ recognizedPerson.class }}</p>
                            <p><strong>打卡时间：</strong>{{ currentTime }}</p>
                        </div>
                        <a-button type="primary" @click="confirmAttendance">确认打卡</a-button>
                    </div>
                    <div v-else-if="isCameraActive" class="recognition-waiting">
                        <loading-outlined spin />
                        <p>请正对摄像头，系统正在识别...</p>
                        <a-button @click="stopCamera">取消</a-button>
                    </div>
                    <div v-if="recentAttendance.length > 0" class="recent-records">
                        <h3>最近打卡记录</h3>
                        <a-list bordered :data-source="recentAttendance">
                            <template #renderItem="{ item }">
                                <a-list-item>
                                    <a-list-item-meta
                                        :title="`${item.name} (${item.id})`"
                                        :description="`${item.time} - ${item.attendance_status}`"
                                    />
                                </a-list-item>
                            </template>
                        </a-list>
                    </div>
                </div>
            </div>
        </div>

        <!-- 考勤详情模态框 -->
        <a-modal
            v-model:visible="detailsModalVisible"
            title="考勤详情"
            :footer="null"
            width="800px">
            <div v-if="selectedPerson">
                <div class="person-details">
                    <h3>{{ selectedPerson.name }}的考勤信息</h3>
                    <p><strong>ID：</strong>{{ selectedPerson.id }}</p>
                    <p><strong>班级/部门：</strong>{{ selectedPerson.class }}</p>
                    <p><strong>角色：</strong>{{ selectedPerson.role === 'teacher' ? '教师' : selectedPerson.role === 'student' ? '学生' : '其他人员' }}</p>
                </div>

                <a-divider />

                <h4>考勤统计</h4>
                <div class="attendance-stats">
                    <a-row :gutter="16">
                        <a-col :span="8">
                            <a-statistic title="出勤率" :value="selectedPerson.stats.attendanceRate" suffix="%" />
                        </a-col>
                        <a-col :span="8">
                            <a-statistic title="出勤天数" :value="selectedPerson.stats.presentDays" />
                        </a-col>
                        <a-col :span="8">
                            <a-statistic title="缺勤天数" :value="selectedPerson.stats.absentDays" />
                        </a-col>
                    </a-row>
                </div>

                <a-divider />

                <h4>考勤记录</h4>
                <a-calendar :fullscreen="false" @select="onCalendarSelect">
                    <template #dateCellRender="value">
                        <div v-if="hasAttendanceRecord(value, selectedPerson)">
                            <a-tag 
                                :color="getAttendanceTagColor(value, selectedPerson)"
                                style="margin: 0">
                                {{ getAttendanceStatus(value, selectedPerson) }}
                            </a-tag>
                        </div>
                    </template>
                </a-calendar>
            </div>
        </a-modal>
    </div>
</template>

<script setup>
import { ref, reactive, computed, onUnmounted } from 'vue';
import { 
    UserOutlined, 
    VideoCameraOutlined,
    CheckCircleOutlined,
    LoadingOutlined
} from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';

// 视图模式：考勤记录/人脸识别打卡
const viewMode = ref('record');

// 表格列定义
const columns = [
    {
        title: '姓名',
        dataIndex: 'name',
        key: 'name',
    },
    {
        title: 'ID',
        dataIndex: 'id',
        key: 'id',
    },
    {
        title: '班级/部门',
        dataIndex: 'class',
        key: 'class',
        filters: [
            { text: '计算机科学与技术系', value: '计算机科学与技术系' },
            { text: '计算机1班', value: '计算机1班' },
            { text: '计算机2班', value: '计算机2班' },
            { text: '设备部', value: '设备部' }
        ],
        onFilter: (value, record) => record.class.indexOf(value) === 0,
    },
    {
        title: '角色',
        dataIndex: 'role',
        key: 'role',
        filters: [
            { text: '教师', value: 'teacher' },
            { text: '学生', value: 'student' },
            { text: '其他', value: 'other' }
        ],
        onFilter: (value, record) => record.role === value,
        render: (text) => text === 'teacher' ? '教师' : text === 'student' ? '学生' : '其他人员'
    },
    {
        title: '日期',
        dataIndex: 'date',
        key: 'date',
        sorter: (a, b) => new Date(a.date) - new Date(b.date),
    },
    {
        title: '打卡时间',
        dataIndex: 'time',
        key: 'time',
    },
    {
        title: '考勤状态',
        dataIndex: 'attendance_status',
        key: 'attendance_status',
        filters: [
            { text: '出勤', value: '出勤' },
            { text: '缺勤', value: '缺勤' },
            { text: '迟到', value: '迟到' }
        ],
        onFilter: (value, record) => record.attendance_status === value,
    },
    {
        title: '操作',
        key: 'action',
    }
];

// 示例考勤数据
const attendanceData = ref([
    {
        key: '1',
        name: '李教授',
        id: 'T20010001',
        class: '计算机科学与技术系',
        role: 'teacher',
        date: '2024-06-10',
        time: '08:15:22',
        attendance_status: '出勤',
    },
    {
        key: '2',
        name: '张三',
        id: 'S202200101',
        class: '计算机2班',
        role: 'student',
        date: '2024-06-10',
        time: '08:20:45',
        attendance_status: '出勤',
    },
    {
        key: '3',
        name: '李四',
        id: 'S202100201',
        class: '计算机1班',
        role: 'student',
        date: '2024-06-10',
        time: '08:45:12',
        attendance_status: '迟到',
    },
    {
        key: '4',
        name: '王维修',
        id: 'O20050001',
        class: '设备部',
        role: 'other',
        date: '2024-06-10',
        time: '08:05:37',
        attendance_status: '出勤',
    },
    {
        key: '5',
        name: '李教授',
        id: 'T20010001',
        class: '计算机科学与技术系',
        role: 'teacher',
        date: '2024-06-09',
        time: '08:12:05',
        attendance_status: '出勤',
    },
    {
        key: '6',
        name: '张三',
        id: 'S202200101',
        class: '计算机2班',
        role: 'student',
        date: '2024-06-09',
        time: '',
        attendance_status: '缺勤',
    },
]);

// 最近打卡记录
const recentAttendance = ref([]);

// 筛选器
const filters = reactive({
    department: '',
    role: '',
    keyword: '',
    startDate: '',
    endDate: '',
});

// 日期范围选择
const dateRange = ref([]);
const onDateRangeChange = (dates, dateStrings) => {
    filters.startDate = dateStrings[0];
    filters.endDate = dateStrings[1];
};

// 搜索
const onSearch = () => {
    // 根据关键字过滤数据
    console.log('搜索关键词：', filters.keyword);
};

// 过滤后的考勤数据
const filteredAttendanceData = computed(() => {
    return attendanceData.value.filter(item => {
        // 班级/部门筛选
        if (filters.department && item.class !== filters.department) {
            return false;
        }
        
        // 角色筛选
        if (filters.role && item.role !== filters.role) {
            return false;
        }
        
        // 日期范围筛选
        if (filters.startDate && filters.endDate) {
            const itemDate = dayjs(item.date);
            if (itemDate.isBefore(dayjs(filters.startDate)) || itemDate.isAfter(dayjs(filters.endDate))) {
                return false;
            }
        }
        
        // 关键词筛选
        if (filters.keyword) {
            const keyword = filters.keyword.toLowerCase();
            return (
                item.name.toLowerCase().includes(keyword) ||
                item.id.toLowerCase().includes(keyword)
            );
        }
        
        return true;
    });
});

// 考勤详情模态框
const detailsModalVisible = ref(false);
const selectedPerson = ref(null);

// 查看考勤详情
const viewAttendanceDetails = (record) => {
    // 查找该人员的所有考勤记录，并生成统计信息
    const personRecords = attendanceData.value.filter(item => item.id === record.id);
    const totalRecords = personRecords.length;
    const presentRecords = personRecords.filter(item => item.attendance_status === '出勤').length;
    const absentRecords = personRecords.filter(item => item.attendance_status === '缺勤').length;
    const lateRecords = personRecords.filter(item => item.attendance_status === '迟到').length;
    
    selectedPerson.value = {
        name: record.name,
        id: record.id,
        class: record.class,
        role: record.role,
        records: personRecords,
        stats: {
            attendanceRate: totalRecords ? Math.round((presentRecords / totalRecords) * 100) : 0,
            presentDays: presentRecords,
            absentDays: absentRecords,
            lateDays: lateRecords
        }
    };
    
    detailsModalVisible.value = true;
};

// 日历选择事件
const onCalendarSelect = (date) => {
    console.log('选择日期：', date.format('YYYY-MM-DD'));
};

// 判断某人在某天是否有考勤记录
const hasAttendanceRecord = (date, person) => {
    const dateStr = date.format('YYYY-MM-DD');
    return attendanceData.value.some(item => 
        item.id === person.id && item.date === dateStr
    );
};

// 获取某人在某天的考勤状态
const getAttendanceStatus = (date, person) => {
    const dateStr = date.format('YYYY-MM-DD');
    const record = attendanceData.value.find(item => 
        item.id === person.id && item.date === dateStr
    );
    return record ? record.attendance_status : '';
};

// 获取考勤标签颜色
const getAttendanceTagColor = (date, person) => {
    const status = getAttendanceStatus(date, person);
    if (status === '出勤') return 'green';
    if (status === '缺勤') return 'red';
    if (status === '迟到') return 'orange';
    return '';
};

// 人脸识别相关
const isCameraActive = ref(false);
const videoElement = ref(null);
const canvasElement = ref(null);
const recognizedPerson = ref(null);
let videoStream = null;

// 获取当前时间
const currentTime = computed(() => {
    return dayjs().format('YYYY-MM-DD HH:mm:ss');
});

// 启动摄像头
const startCamera = async () => {
    try {
        videoStream = await navigator.mediaDevices.getUserMedia({ 
            video: { facingMode: 'user' } 
        });
        
        if (videoElement.value) {
            videoElement.value.srcObject = videoStream;
            isCameraActive.value = true;
            
            // 模拟人脸识别过程
            setTimeout(() => {
                simulateFaceRecognition();
            }, 3000);
        }
    } catch (err) {
        message.error('无法访问摄像头，请检查权限设置');
        console.error('摄像头访问错误：', err);
    }
};

// 停止摄像头
const stopCamera = () => {
    if (videoStream) {
        videoStream.getTracks().forEach(track => track.stop());
        videoStream = null;
    }
    isCameraActive.value = false;
    recognizedPerson.value = null;
};

// 模拟人脸识别（在实际应用中，这里应该连接到真实的人脸识别API）
const simulateFaceRecognition = () => {
    // 模拟识别到的人员信息（实际应用中，这应该是从人脸识别API返回的结果）
    const mockPerson = {
        name: '张三',
        id: 'S202200101',
        class: '计算机2班',
        role: 'student'
    };
    
    // 绘制人脸框
    if (videoElement.value && canvasElement.value) {
        const video = videoElement.value;
        const canvas = canvasElement.value;
        
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        
        const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // 绘制识别框
        ctx.strokeStyle = '#00ff00';
        ctx.lineWidth = 3;
        
        // 假设人脸位于视频中央
        const faceX = canvas.width * 0.3;
        const faceY = canvas.height * 0.3;
        const faceWidth = canvas.width * 0.4;
        const faceHeight = canvas.height * 0.4;
        
        ctx.strokeRect(faceX, faceY, faceWidth, faceHeight);
        
        // 显示识别到的人员
        recognizedPerson.value = mockPerson;
    }
};

// 确认打卡
const confirmAttendance = () => {
    if (!recognizedPerson.value) return;
    
    // 创建新的考勤记录
    const newAttendance = {
        key: Date.now().toString(),
        name: recognizedPerson.value.name,
        id: recognizedPerson.value.id,
        class: recognizedPerson.value.class,
        role: recognizedPerson.value.role,
        date: dayjs().format('YYYY-MM-DD'),
        time: dayjs().format('HH:mm:ss'),
        attendance_status: '出勤',  // 在实际应用中，可能需要根据时间判断是否迟到
    };
    
    // 添加到考勤数据
    attendanceData.value.unshift(newAttendance);
    
    // 添加到最近打卡记录
    recentAttendance.value.unshift(newAttendance);
    if (recentAttendance.value.length > 5) {
        recentAttendance.value.pop();
    }
    
    message.success(`${recognizedPerson.value.name} 打卡成功！`);
    
    // 重置摄像头
    stopCamera();
};

// 组件卸载时清理摄像头
onUnmounted(() => {
    stopCamera();
});
</script>

<style scoped>
.attendance-container {
    padding: 16px;
}

.attendance-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.search-bar {
    margin-bottom: 16px;
}

.attendance-check {
    margin-top: 24px;
}

.facial-recognition-container {
    display: flex;
    gap: 24px;
}

.camera-view {
    width: 480px;
    height: 360px;
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
}

.camera-placeholder {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: #f5f5f5;
}

.camera-placeholder .anticon {
    font-size: 48px;
    color: #bfbfbf;
    margin-bottom: 16px;
}

.camera-active {
    height: 100%;
    position: relative;
}

.camera-feed {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.face-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.recognition-results {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.recognition-success, 
.recognition-waiting {
    padding: 16px;
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    text-align: center;
}

.recognition-success .anticon {
    font-size: 48px;
    color: #52c41a;
    margin-bottom: 16px;
}

.recognition-waiting .anticon {
    font-size: 48px;
    color: #1890ff;
    margin-bottom: 16px;
}

.person-info {
    text-align: left;
    margin: 16px 0;
}

.recent-records {
    margin-top: 16px;
    flex: 1;
}

.recent-records h3 {
    margin-bottom: 12px;
}

.person-details {
    margin-bottom: 16px;
}

.attendance-stats {
    margin: 16px 0;
}
</style> 