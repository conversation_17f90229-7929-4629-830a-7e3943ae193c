<script setup>
import { ref, computed, onUnmounted, h, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import { VideoCameraOutlined, CheckCircleOutlined, LoadingOutlined, PlayCircleOutlined, SmileOutlined, DownOutlined, ClockCircleOutlined, ExclamationCircleOutlined, BarChartOutlined } from '@ant-design/icons-vue';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, LineChart } from 'echarts/charts';
import { TitleComponent, TooltipComponent, LegendComponent, GridComponent } from 'echarts/components';
import VChart from 'vue-echarts';

use([
  CanvasRenderer,
  Pie<PERSON>hart,
  BarChart,
  LineChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
]);

const router = useRouter();
const mode = ref('face'); // 'face' or 'video'

const options = [
  {
    code: '实时',
    name: '实时',
  },
  {
    code: '回放',
    name: '回放',
    items: [
        
    ],
  },
];
const value = ref([]);
const dateTime = ref();
const value1 = ref();

// 判断是否选择了回放
const isPlayback = computed(() => {
  if (Array.isArray(value.value)) {
    return value.value.includes('回放');
  }
  return value.value === '回放';
});

// 事件卡片相关
const eventFilter = ref('unhandled'); // 'unhandled' | 'all'
const eventCollapse = ref({}); // 控制每个分组下拉
const eventList = ref([
  { id: 1, type: '异常闯入', time: '2024-06-12 10:23:11', status: '未处理', desc: '检测到异常闯入', camera: '摄像头1' },
  { id: 2, type: '设备故障', time: '2024-06-12 09:50:02', status: '未处理', desc: '摄像头2信号丢失', camera: '摄像头2' },
  { id: 3, type: '正常巡检', time: '2024-06-11 17:20:00', status: '已处理', desc: '例行巡检', camera: '摄像头1' },
  { id: 4, type: '异常闯入', time: '2024-06-11 15:10:45', status: '未处理', desc: '检测到异常闯入', camera: '摄像头3' },
  { id: 5, type: '设备故障', time: '2024-06-10 13:05:22', status: '已处理', desc: '摄像头1信号恢复', camera: '摄像头1' },
]);

const groupedEvents = computed(() => {
  // 按日期分组
  const groups = {};
  eventList.value.forEach(ev => {
    const date = ev.time.split(' ')[0];
    if (!groups[date]) groups[date] = [];
    groups[date].push(ev);
  });
  // 按时间倒序
  return Object.entries(groups).sort((a, b) => b[0].localeCompare(a[0]));
});

const filteredEvents = (events) => {
  if (eventFilter.value === 'unhandled') {
    return events.filter(ev => ev.status === '未处理');
  }
  return events;
};

const toggleCollapse = (date) => {
  eventCollapse.value[date] = !eventCollapse.value[date];
};

// 人脸识别相关
const isCameraActive = ref(false);
const videoElement = ref(null);
const canvasElement = ref(null);
const recognizedPerson = ref(null);
let videoStream = null;

const currentTime = computed(() => {
    const now = new Date();
    return now.getFullYear() + '-' + String(now.getMonth()+1).padStart(2,'0') + '-' + String(now.getDate()).padStart(2,'0') + ' ' + String(now.getHours()).padStart(2,'0') + ':' + String(now.getMinutes()).padStart(2,'0') + ':' + String(now.getSeconds()).padStart(2,'0');
});

const startCamera = async () => {
    try {
        videoStream = await navigator.mediaDevices.getUserMedia({ 
            video: { facingMode: 'user' } 
        });
        if (videoElement.value) {
            videoElement.value.srcObject = videoStream;
            isCameraActive.value = true;
            setTimeout(() => {
                simulateFaceRecognition();
            }, 3000);
        }
    } catch (err) {
        message.error('无法访问摄像头，请检查权限设置');
        console.error('摄像头访问错误：', err);
    }
};

const stopCamera = () => {
    if (videoStream) {
        videoStream.getTracks().forEach(track => track.stop());
        videoStream = null;
    }
    isCameraActive.value = false;
    recognizedPerson.value = null;
};

// 跳转到聊天页面
const goToChat = () => {
    router.push('/chat');
};

const simulateFaceRecognition = () => {
    const mockPerson = {
        name: '张三',
        id: 'S202200101',
        class: '计算机2班',
        role: 'student',
        time: currentTime.value,
        status: '识别成功',
    };
    if (videoElement.value && canvasElement.value) {
        const video = videoElement.value;
        const canvas = canvasElement.value;
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.strokeStyle = '#00ff00';
        ctx.lineWidth = 3;
        const faceX = canvas.width * 0.3;
        const faceY = canvas.height * 0.3;
        const faceWidth = canvas.width * 0.4;
        const faceHeight = canvas.height * 0.4;
        ctx.strokeRect(faceX, faceY, faceWidth, faceHeight);
        recognizedPerson.value = mockPerson;
    }
};

const faceDataList = ref([
  { name: '张三', id: 'S202200101', class: '计算机2班', time: '2024-06-12 10:23:11', status: '识别成功' },
  { name: '李四', id: 'S202200102', class: '计算机1班', time: '2024-06-12 09:50:02', status: '识别成功' },
  { name: '未知人员', id: '---', class: '设备部', time: '2024-06-11 17:20:00', status: '识别失败' },
  { name: '赵六', id: 'S202200104', class: '计算机3班', time: '2024-06-12 08:15:30', status: '识别成功' },
  { name: '未知人员', id: '---', class: '---', time: '2024-06-12 07:45:22', status: '识别失败' },
]);

// 动态计算人脸识别统计数据
const faceRecognitionStats = computed(() => {
  const successCount = faceDataList.value.filter(item => item.status === '识别成功').length;
  const failCount = faceDataList.value.filter(item => item.status === '识别失败').length;
  const total = faceDataList.value.length;

  return {
    success: successCount,
    fail: failCount,
    total: total,
    successRate: total > 0 ? ((successCount / total) * 100).toFixed(1) : 0
  };
});

// ECharts 图表配置
const faceRecognitionPieOption = computed(() => ({
  // title: {
  //   text: '人脸识别统计',
  //   left: 'center',
  //   textStyle: {
  //     fontSize: 16,
  //     fontWeight: 'bold'
  //   }
  // },
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c}人 ({d}%)'
  },
  legend: {
    orient: 'vertical',
    left: 'left',
    bottom: 10
  },
  series: [
    {
      name: '识别结果',
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['50%', '55%'],
      data: [
        {
          value: faceRecognitionStats.value.success,
          name: '识别成功',
          itemStyle: { color: '#52c41a' }
        },
        {
          value: faceRecognitionStats.value.fail,
          name: '识别失败',
          itemStyle: { color: '#ff7875' }
        }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
}));

// 动态计算事件统计数据
const eventStats = computed(() => {
  const unhandledCount = eventList.value.filter(item => item.status === '未处理').length;
  const processingCount = eventList.value.filter(item => item.status === '处理中').length;
  const handledCount = eventList.value.filter(item => item.status === '已处理').length;

  return {
    unhandled: unhandledCount,
    processing: processingCount,
    handled: handledCount,
    total: eventList.value.length
  };
});

const eventStatusBarOption = computed(() => ({
  title: {
    text: '事件处理状态',
    left: 'center',
    textStyle: {
      fontSize: 16,
      fontWeight: 'bold'
    }
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    },
    formatter: '{a} <br/>{b}: {c}个事件'
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['未处理', '处理中', '已处理']
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '事件数量',
      type: 'bar',
      data: [
        { value: eventStats.value.unhandled, itemStyle: { color: '#faad14' } },
        { value: eventStats.value.processing, itemStyle: { color: '#1890ff' } },
        { value: eventStats.value.handled, itemStyle: { color: '#52c41a' } }
      ],
      barWidth: '60%'
    }
  ]
}));

// 生成最近7天的数据
const generateDailyData = () => {
  const dates = [];
  const data = [];
  for (let i = 6; i >= 0; i--) {
    const date = new Date();
    date.setDate(date.getDate() - i);
    dates.push(`${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`);
    // 模拟数据，实际应用中应该从真实数据计算
    data.push(Math.floor(Math.random() * 20) + 10);
  }
  return { dates, data };
};

const dailyTrendLineOption = computed(() => {
  const { dates, data } = generateDailyData();
  return {
    title: {
      text: '每日识别趋势',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      formatter: '{a} <br/>{b}: {c}次'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: dates
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '识别次数',
        type: 'line',
        stack: 'Total',
        data: data,
        itemStyle: { color: '#1890ff' },
        areaStyle: { color: 'rgba(24, 144, 255, 0.1)' }
      }
    ]
  };
});

// 摄像头状态统计图表
const cameraStatusOption = computed(() => ({
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c}个 ({d}%)'
  },
  legend: {
    orient: 'vertical',
    left: 'left',
    bottom: 10
  },
  series: [
    {
      name: '摄像头状态',
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['50%', '55%'],
      data: [
        { value: 2, name: '正常运行', itemStyle: { color: '#52c41a' } },
        { value: 1, name: '信号丢失', itemStyle: { color: '#ff7875' } },
        { value: 0, name: '维护中', itemStyle: { color: '#faad14' } }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
}));

// 实时数据更新
const updateChartData = () => {
  // 这里可以添加实时数据更新逻辑
  // 例如从API获取最新数据
};

onMounted(() => {
  // 初始化图表数据
  updateChartData();
  // 可以设置定时器定期更新数据
  // setInterval(updateChartData, 30000); // 每30秒更新一次
});

onUnmounted(() => {
    stopCamera();
});
</script>

<template>
  <div class="home-bg">
    <div class="home-container">
      <div class="mode-switch-card">
        <div class="mode-switch-content">
          <a-segmented
            v-model:value="mode"
            :options="[
              { label: '人脸识别', value: 'face', icon: () => h(SmileOutlined) },
              { label: '智慧监控', value: 'video', icon: () => h(PlayCircleOutlined) }
            ]"
            size="large"
            class="mode-segmented"
          />
          <a-button
            type="primary"
            size="large"
            class="chat-btn"
            @click="goToChat"
          >
            实时通讯
          </a-button>
        </div>
      </div>
      <transition name="fade-slide" mode="out-in">
        <div v-if="mode==='face'" key="face" class="main-row">
          <!-- 左侧图表面板 -->
          <div class="charts-panel">
            <div class="chart-card">
              <div class="chart-title">
                <BarChartOutlined style="color:#1890ff; margin-right:8px;"/>
                人脸识别统计
              </div>
              <div class="chart-stats">
                <div class="stat-item">
                  <span class="stat-label">总识别次数:</span>
                  <span class="stat-value">{{ faceRecognitionStats.total }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">成功率:</span>
                  <span class="stat-value success">{{ faceRecognitionStats.successRate }}%</span>
                </div>
              </div>
              <div class="chart-container">
                <v-chart :option="faceRecognitionPieOption" />
              </div>
            </div>
            <div class="chart-card">
              <div class="chart-title">
                <ExclamationCircleOutlined style="color:#faad14; margin-right:8px;"/>
                事件处理状态
              </div>
              <div class="chart-stats">
                <div class="stat-item">
                  <span class="stat-label">待处理:</span>
                  <span class="stat-value warning">{{ eventStats.unhandled }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">已处理:</span>
                  <span class="stat-value success">{{ eventStats.handled }}</span>
                </div>
              </div>
              <div class="chart-container">
                <v-chart :option="eventStatusBarOption" />
              </div>
            </div>
            <div class="chart-card">
              <div class="chart-title">
                <ClockCircleOutlined style="color:#1890ff; margin-right:8px;"/>
                识别趋势分析
              </div>
              <div class="chart-container">
                <v-chart :option="dailyTrendLineOption" />
              </div>
            </div>
          </div>
          <div class="face-recognition-section card">
            <h2 class="section-title"><SmileOutlined style="color:#1890ff; margin-right:8px;"/>实时人脸识别</h2>
            <div class="facial-recognition-container">
              <div class="camera-view">
                <div v-if="!isCameraActive" class="camera-placeholder">
                  <VideoCameraOutlined style="font-size: 48px; color: #bfbfbf; margin-bottom: 16px;" />
                  <p>点击启动摄像头进行识别</p>
                  <a-button type="primary" @click="startCamera">启动摄像头</a-button>
                </div>
                <div v-else class="camera-active">
                  <video ref="videoElement" autoplay class="camera-feed"></video>
                  <canvas ref="canvasElement" class="face-canvas"></canvas>
                </div>
              </div>
              <div class="recognition-results">
                <div v-if="recognizedPerson" class="recognition-success">
                  <CheckCircleOutlined style="color: #52c41a; font-size: 32px;" />
                  <h3>识别成功</h3>
                  <div class="person-info">
                    <p><strong>姓名：</strong>{{ recognizedPerson.name }}</p>
                    <p><strong>ID：</strong>{{ recognizedPerson.id }}</p>
                    <p><strong>班级/部门：</strong>{{ recognizedPerson.class }}</p>
                    <p><strong>识别时间：</strong>{{ currentTime }}</p>
                  </div>
                  <a-button type="primary" @click="stopCamera">关闭摄像头</a-button>
                </div>
                <div v-else-if="isCameraActive" class="recognition-waiting">
                  <LoadingOutlined spin style="font-size: 32px; color: #1890ff;" />
                  <p>请正对摄像头，系统正在识别...</p>
                  <a-button @click="stopCamera">取消</a-button>
                </div>
              </div>
            </div>
          </div>
          <div class="side-card">
            <div class="side-title"><CheckCircleOutlined style="color:#52c41a; margin-right:6px;"/>人脸识别数据</div>
            <div class="face-data-list">
              <div v-for="item in faceDataList" :key="item.id" class="face-data-item" :class="{'fail': item.status!=='识别成功'}">
                <div class="face-data-row">
                  <span class="face-name">{{ item.name }}</span>
                  <span class="face-status" :class="{'success': item.status==='识别成功'}">{{ item.status }}</span>
                </div>
                <div class="face-data-row small">
                  <span>ID: {{ item.id }}</span>
                  <span>{{ item.class }}</span>
                </div>
                <div class="face-data-row small">
                  <ClockCircleOutlined style="margin-right:4px;"/>{{ item.time }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else key="video" class="main-row">
          <!-- 左侧图表面板 -->
          <div class="charts-panel">
            <div class="chart-card">
              <div class="chart-title">
                <VideoCameraOutlined style="color:#faad14; margin-right:8px;"/>
                摄像头状态监控
              </div>
              <div class="chart-stats">
                <div class="stat-item">
                  <span class="stat-label">在线摄像头:</span>
                  <span class="stat-value success">2</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">离线摄像头:</span>
                  <span class="stat-value error">1</span>
                </div>
              </div>
              <div class="chart-container">
                <v-chart :option="cameraStatusOption" />
              </div>
            </div>
            <div class="chart-card">
              <div class="chart-title">
                <ExclamationCircleOutlined style="color:#faad14; margin-right:8px;"/>
                事件监控统计
              </div>
              <div class="chart-stats">
                <div class="stat-item">
                  <span class="stat-label">今日事件:</span>
                  <span class="stat-value">{{ eventStats.total }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">待处理:</span>
                  <span class="stat-value warning">{{ eventStats.unhandled }}</span>
                </div>
              </div>
              <div class="chart-container">
                <v-chart :option="eventStatusBarOption" />
              </div>
            </div>
            <div class="chart-card">
              <div class="chart-title">
                <ClockCircleOutlined style="color:#1890ff; margin-right:8px;"/>
                监控活动趋势
              </div>
              <div class="chart-container">
                <v-chart :option="dailyTrendLineOption" />
              </div>
            </div>
          </div>
          <div class="video-section card">
            <h2 class="section-title"><PlayCircleOutlined style="color:#faad14; margin-right:8px;"/>智慧监控</h2>
            <div class="camera-select-section">
              <span>摄像头：</span>
              <a-select v-model:value="value1" style="width: 200px" placeholder="选择摄像头">
                <a-select-option value="1">摄像头1</a-select-option>
                <a-select-option value="2">摄像头2</a-select-option>
                <a-select-option value="3">摄像头3</a-select-option>
              </a-select>
              <a-cascader v-model:value="value" :field-names="{ label: 'name', value: 'code', children: 'items' }"
                :options="options" placeholder="请选择模式" style="margin-left: 16px; width: 200px;" />
                        <div v-if="isPlayback" style="margin: 16px 0;">
              <span>时间：</span>
              <a-date-picker v-model:value="dateTime" show-time style="width: 240px" placeholder="请选择日期和时间" />
            </div>
              </div>

            <div v-if="isPlayback" class="video-player-wrap">
              <video width="1000" controls>
                <source src="../../public/1.mp4" type="video/mp4">
              </video>
            </div>
            <div v-else class="video-player-wrap">
              <div class="video-placeholder">
                <VideoCameraOutlined style="font-size: 64px; color: #bfbfbf; margin-bottom: 16px;" />
                <p class="placeholder-title">无法连接摄像头</p>
                <p class="placeholder-desc">请检查摄像头连接或选择正确的摄像头</p>
              </div>
            </div>
          </div>
          <div class="side-card">
            <div class="side-title"><ExclamationCircleOutlined style="color:#faad14; margin-right:6px;"/>检测事件</div>
            <div class="event-filter">
              <a-radio-group v-model:value="eventFilter" size="small">
                <a-radio-button value="unhandled">未处理</a-radio-button>
                <a-radio-button value="all">全部</a-radio-button>
              </a-radio-group>
            </div>
            <div class="event-list">
              <div v-for="([date, events]) in groupedEvents" :key="date" class="event-group">
                <div class="event-group-header" @click="toggleCollapse(date)">
                  <span><ClockCircleOutlined style="margin-right:4px;"/>{{ date }}</span>
                  <DownOutlined :class="{'rotated': eventCollapse[date]}" />
                </div>
                <transition name="fade-slide">
                  <div v-show="eventCollapse[date] !== false" class="event-group-body">
                    <div v-for="ev in filteredEvents(events)" :key="ev.id" class="event-item" :class="{'unhandled': ev.status==='未处理'}">
                      <div class="event-type">{{ ev.type }}</div>
                      <div class="event-desc">{{ ev.desc }}</div>
                      <div class="event-meta">
                        <span>{{ ev.time.split(' ')[1] }}</span>
                        <span>{{ ev.camera }}</span>
                        <span class="event-status" :class="{'unhandled': ev.status==='未处理'}">{{ ev.status }}</span>
                      </div>
                    </div>
                  </div>
                </transition>
              </div>
            </div>
          </div>
        </div>
      </transition>
    </div>
  </div>
</template>

<style scoped>
.home-bg {
  min-height: 92vh;
  background: linear-gradient(135deg, #e0e7ff 0%, #f0f5ff 100%);
  padding: 0;
}
.home-container {
  max-width: 1800px;
  margin: 0 auto;
  padding: 48px 20px 32px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.mode-switch-card {
  display: flex;
  justify-content: center;
  margin-bottom: 40px;
  width: 100%;
}

.mode-switch-content {
  display: flex;
  align-items: center;
  gap: 24px;
}
.mode-segmented {
  background: #fff;
  border-radius: 32px;
  box-shadow: 0 4px 24px rgba(24,144,255,0.08);
  padding: 8px 24px;
  font-size: 18px;
}

.chat-btn {
  background: linear-gradient(135deg, #07c160 0%, #06ad56 100%);
  border: none;
  border-radius: 24px;
  box-shadow: 0 4px 16px rgba(7, 193, 96, 0.3);
  font-size: 16px;
  font-weight: 500;
  height: 48px;
  padding: 0 24px;
  transition: all 0.3s ease;
}

.chat-btn:hover {
  background: linear-gradient(135deg, #06ad56 0%, #059c4f 100%);
  box-shadow: 0 6px 20px rgba(7, 193, 96, 0.4);
  transform: translateY(-2px);
}
.main-row {
  display: flex;
  gap: 28px;
  align-items: flex-start;
  justify-content: space-between;
  width: 100%;
  max-width: 1760px;
  margin: 0 auto;
}

/* 左侧图表面板样式 */
.charts-panel {
  width: 320px;
  min-width: 320px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 75vh;
}

.chart-card {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.08);
  padding: 20px;
  animation: fadeInCard 0.7s cubic-bezier(.4,0,.2,1);
  transition: transform 0.2s, box-shadow 0.2s;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  position: relative;
  overflow: visible;
}

.chart-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(0,0,0,0.12);
}

.chart-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  color: #333;
}

.chart-container {
  width: 100%;
  flex: 1;
  min-height: 180px;
  position: relative;
  z-index: 1;
  overflow: visible;
}

.chart-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  padding: 8px 12px;
  background: #f8faff;
  border-radius: 8px;
  border: 1px solid #e6f7ff;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.stat-value.success {
  color: #52c41a;
}

.stat-value.warning {
  color: #faad14;
}

.stat-value.error {
  color: #ff7875;
}

/* ECharts tooltip样式修复 */
.echarts {
  position: relative;
  z-index: 10;
}

/* 确保tooltip不被遮挡 */
.charts-panel {
  position: relative;
  z-index: 5;
}
.card {
  background: #fff;
  border-radius: 18px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.08);
  padding: 40px 36px 36px 36px;
  margin-bottom: 32px;
  animation: fadeInCard 0.7s cubic-bezier(.4,0,.2,1);
  flex: 1;
  min-width: 600px;
  height: 69.5vh;
  display: flex;
  flex-direction: column;
}
@keyframes fadeInCard {
  from { opacity: 0; transform: translateY(40px); }
  to { opacity: 1; transform: translateY(0); }
}
.section-title {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 36px;
  letter-spacing: 1px;
  display: flex;
  align-items: center;
}
.facial-recognition-container {
  align-items: center;
  width: 100%;
  flex: 1;
  min-height: 0;
}
.camera-view {
  width: 600px;
  height: 480px;
  border: 1.5px solid #dbeafe;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
  background: linear-gradient(120deg, #f0f5ff 60%, #e0e7ff 100%);
  box-shadow: 0 2px 12px rgba(24,144,255,0.07);
  transition: box-shadow 0.2s;
  margin: 0 auto;
}
.camera-view:hover {
  box-shadow: 0 6px 32px rgba(24,144,255,0.13);
}
.camera-placeholder {
  height: 100%;
  min-height: 480px;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  transition: background 0.2s;
}
.camera-active {
  height: 100%;
  position: relative;
}
.camera-feed {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.face-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}
.recognition-results {
  flex: 1;
  min-width: 300px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  justify-content: center;
}
.recognition-success, .recognition-waiting {
  padding: 32px 24px;
  border: 1.5px solid #e6f7ff;
  border-radius: 12px;
  background: #fafdff;
  text-align: center;
  box-shadow: 0 2px 8px rgba(24,144,255,0.04);
  transition: box-shadow 0.2s;
  min-width: 280px;
}
.recognition-success:hover, .recognition-waiting:hover {
  box-shadow: 0 6px 24px rgba(82,196,26,0.10);
}
.person-info {
  margin: 20px 0;
  text-align: left;
  font-size: 18px;
  line-height: 1.6;
}

.person-info p {
  margin-bottom: 8px;
}
.camera-select-section {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  margin-bottom: 28px;
  font-size: 16px;
}

.camera-select-section .ant-select,
.camera-select-section .ant-cascader {
  min-width: 220px;
}
.video-player-wrap {
  margin-top: 24px;
  display: flex;
  justify-content: center;
}

.video-player-wrap video {
  max-width: 1050px;
  height: auto;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0,0,0,0.1);
}
.fade-slide-enter-active, .fade-slide-leave-active {
  transition: all 0.5s cubic-bezier(.4,0,.2,1);
}
.fade-slide-enter-from, .fade-slide-leave-to {
  opacity: 0;
  transform: translateY(40px);
}
.video-placeholder {
  width: 100%;
  max-width: 800px;
  height: 480px;
  background: linear-gradient(120deg, #f0f5ff 60%, #e0e7ff 100%);
  border: 1.5px dashed #dbeafe;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  box-shadow: 0 2px 12px rgba(24,144,255,0.07);
  text-align: center;
}
.placeholder-title {
  font-size: 22px;
  color: #888;
  font-weight: 600;
  margin-bottom: 8px;
}
.placeholder-desc {
  color: #aaa;
  font-size: 15px;
}
/* 右侧卡片样式 */
.side-card {
  width: 320px;
  height: 72vh;
  min-width: 320px;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.08);
  padding: 28px 22px 18px 22px;
  margin-left: 0;
  display: flex;
  flex-direction: column;
  gap: 18px;
  overflow-y: auto;
}
.side-title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}
.face-data-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.face-data-item {
  background: #f6faff;
  border-radius: 10px;
  padding: 12px 14px 8px 14px;
  box-shadow: 0 1px 4px rgba(24,144,255,0.04);
  margin-bottom: 2px;
  border-left: 4px solid #52c41a;
  transition: border-color 0.2s;
}
.face-data-item.fail {
  border-left-color: #ff7875;
  background: #fff6f6;
}
.face-data-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  margin-bottom: 2px;
}
.face-data-row.small {
  font-size: 13px;
  color: #888;
}
.face-status.success {
  color: #52c41a;
  font-weight: 600;
}
.face-status {
  font-weight: 600;
}
/* 事件卡片 */
.event-filter {
  margin-bottom: 10px;
}
.event-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.event-group {
  margin-bottom: 10px;
  background: #fafdff;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(24,144,255,0.04);
}
.event-group-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 15px;
  font-weight: 600;
  padding: 8px 10px 8px 8px;
  cursor: pointer;
  border-bottom: 1px solid #e6f7ff;
  user-select: none;
}
.event-group-header .anticon {
  transition: transform 0.3s;
}
.event-group-header .rotated {
  transform: rotate(180deg);
}
.event-group-body {
  padding: 8px 10px 6px 18px;
}
.event-item {
  padding: 7px 0 5px 0;
  border-bottom: 1px dashed #e6f7ff;
  font-size: 15px;
  color: #333;
  display: flex;
  flex-direction: column;
  gap: 2px;
}
.event-item:last-child {
  border-bottom: none;
}
.event-item.unhandled {
  background: #fffbe6;
}
.event-type {
  font-weight: 600;
  color: #faad14;
}
.event-desc {
  color: #888;
  font-size: 14px;
}
.event-meta {
  display: flex;
  gap: 12px;
  font-size: 13px;
  color: #999;
  margin-top: 2px;
}
.event-status.unhandled {
  color: #fa541c;
  font-weight: 600;
}
.event-status {
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .main-row {
    max-width: 1200px;
    padding: 0 20px;
  }
  .charts-panel {
    width: 280px;
    min-width: 280px;
    height: 70vh;
  }
  .side-card {
    width: 280px;
    min-width: 280px;
    height: 70vh;
  }
  .card {
    height: 70vh;
  }
  .camera-view {
    height: 400px;
  }
  .camera-placeholder {
    min-height: 400px;
  }
  .video-placeholder {
    height: 400px;
  }
}

@media (max-width: 1200px) {
  .main-row {
    flex-direction: column;
    align-items: center;
    gap: 20px;
    padding: 0 20px;
  }
  .home-container {
    padding: 48px 10px 32px 10px;
  }
  .charts-panel {
    width: 100%;
    max-width: 800px;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    height: auto;
  }
  .chart-card {
    flex: 1;
    min-width: 250px;
    max-width: 350px;
    height: 300px;
  }
  .side-card {
    width: 100%;
    max-width: 800px;
    height: auto;
    max-height: 400px;
  }
  .card {
    height: auto;
    min-height: 500px;
    width: 100%;
    max-width: 800px;
  }
}

@media (max-width: 768px) {
  .charts-panel {
    flex-direction: column;
    height: auto;
  }
  .chart-card {
    max-width: none;
    height: 250px;
  }
  .card {
    min-height: 400px;
  }
  .mode-switch-card {
    flex-direction: column;
    gap: 16px;
  }

  .mode-switch-content {
    flex-direction: column;
    gap: 16px;
  }

  .chat-btn {
    width: 100%;
    max-width: 200px;
  }
  .chat-window {
    width: 95vw;
    height: 80vh;
  }
}

/* 聊天窗口样式 */
.chat-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.chat-window {
  width: 500px;
  height: 600px;
  max-width: 90vw;
  max-height: 90vh;
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  animation: slideInUp 0.3s ease;
}

/* 聊天窗口动画 */
.slide-fade-enter-active {
  transition: all 0.3s ease;
}

.slide-fade-leave-active {
  transition: all 0.3s ease;
}

.slide-fade-enter-from {
  opacity: 0;
  transform: translateY(30px);
}

.slide-fade-leave-to {
  opacity: 0;
  transform: translateY(-30px);
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
</style>
