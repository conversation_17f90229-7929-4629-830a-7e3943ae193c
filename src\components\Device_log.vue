<template>
  <div class="device-log-container">
    <div class="header">
      <h2>设备维修与保养记录</h2>
      <div class="header-buttons">
        <a-button type="primary" @click="showAddModal">添加维修记录</a-button>
        <a-button @click="fetchFaultReports">刷新安全检查工单</a-button>
      </div>
    </div>
    
    <a-table :columns="columns" :data-source="maintenanceData" :row-key="record => record.key">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'status'">
          <a-tag :color="getStatusColor(record.status)">{{ record.status }}</a-tag>
        </template>
        <template v-if="column.dataIndex === 'source'">
          <a-tag color="blue" v-if="record.fromSafetyCheck">安全检查</a-tag>
          <span v-else>常规维护</span>
        </template>
        <template v-if="column.dataIndex === 'urgency'">
          <a-tag color="orange" v-if="record.urgency === '紧急'">{{ record.urgency }}</a-tag>
          <a-tag v-else-if="record.urgency">{{ record.urgency }}</a-tag>
          <span v-else>-</span>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <div class="action-buttons">
            <a-button type="primary" size="small" @click="showCompleteModal(record)" v-if="record.status === '进行中'">完成维修</a-button>
            <a-button size="small" @click="viewMaintenanceDetails(record)">查看详情</a-button>
            <a-button type="primary" danger size="small" @click="showDeleteConfirm(record)">删除记录</a-button>
          </div>
        </template>
      </template>
    </a-table>

    <!-- 添加维修记录弹窗 -->
    <a-modal
      v-model:visible="addModalVisible"
      title="添加维修记录"
      @ok="handleAddOk"
      @cancel="handleAddCancel"
      okText="确认"
      cancelText="取消"
      width="700px"
    >
      <a-form :model="formState" layout="vertical">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="设备名称" name="deviceName" :rules="[{ required: true, message: '请选择设备' }]">
              <a-select v-model:value="formState.deviceName" placeholder="选择设备">
                <a-select-option v-for="device in deviceList" :key="device.key" :value="device.name">
                  {{ device.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="维修类型" name="maintenanceType" :rules="[{ required: true, message: '请选择维修类型' }]">
              <a-select v-model:value="formState.maintenanceType">
                <a-select-option value="计划保养">计划保养</a-select-option>
                <a-select-option value="故障维修">故障维修</a-select-option>
                <a-select-option value="紧急维修">紧急维修</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="开始日期" name="startDate" :rules="[{ required: true, message: '请选择开始日期' }]">
              <a-date-picker v-model:value="formState.startDate" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="预计完成日期" name="estimatedEndDate">
              <a-date-picker v-model:value="formState.estimatedEndDate" style="width: 100%" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-form-item label="维修人员" name="maintenanceStaff" :rules="[{ required: true, message: '请输入维修人员' }]">
          <a-input v-model:value="formState.maintenanceStaff" />
        </a-form-item>
        <a-form-item label="联系电话" name="contactPhone">
          <a-input v-model:value="formState.contactPhone" />
        </a-form-item>
        <a-form-item label="故障描述/维修内容" name="description" :rules="[{ required: true, message: '请输入维修内容' }]">
          <a-textarea v-model:value="formState.description" :rows="3" />
        </a-form-item>
        <a-form-item label="需要更换零部件" name="partsToReplace">
          <a-textarea v-model:value="formState.partsToReplace" :rows="2" placeholder="如有需要更换的零部件，请在此填写" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 完成维修弹窗 -->
    <a-modal
      v-model:visible="completeModalVisible"
      title="完成维修"
      @ok="handleCompleteOk"
      @cancel="handleCompleteCancel"
      okText="确认完成"
      cancelText="取消"
      width="700px"
    >
      <div v-if="currentMaintenance">
        <a-descriptions bordered title="维修任务信息" size="small" :column="2">
          <a-descriptions-item label="设备名称">{{ currentMaintenance.deviceName }}</a-descriptions-item>
          <a-descriptions-item label="维修类型">{{ currentMaintenance.maintenanceType }}</a-descriptions-item>
          <a-descriptions-item label="开始日期">{{ formatDate(currentMaintenance.startDate) }}</a-descriptions-item>
          <a-descriptions-item label="预计完成日期">{{ formatDate(currentMaintenance.estimatedEndDate) }}</a-descriptions-item>
          <a-descriptions-item label="维修人员">{{ currentMaintenance.maintenanceStaff }}</a-descriptions-item>
          <a-descriptions-item label="联系电话">{{ currentMaintenance.contactPhone }}</a-descriptions-item>
        </a-descriptions>
        
        <a-divider />
        
        <a-form :model="completeFormState" layout="vertical">
          <a-form-item label="实际完成日期" name="actualEndDate" :rules="[{ required: true, message: '请选择完成日期' }]">
            <a-date-picker v-model:value="completeFormState.actualEndDate" style="width: 100%" />
          </a-form-item>
          <a-form-item label="维修结果" name="result" :rules="[{ required: true, message: '请选择维修结果' }]">
            <a-radio-group v-model:value="completeFormState.result">
              <a-radio value="已解决">已解决</a-radio>
              <a-radio value="部分解决">部分解决</a-radio>
              <a-radio value="未解决">未解决</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item label="实际更换零部件" name="partsReplaced">
            <a-textarea v-model:value="completeFormState.partsReplaced" :rows="2" placeholder="如有更换的零部件，请在此填写" />
          </a-form-item>
          <a-form-item label="维修详情" name="maintenanceDetails" :rules="[{ required: true, message: '请输入维修详情' }]">
            <a-textarea v-model:value="completeFormState.maintenanceDetails" :rows="3" />
          </a-form-item>
          <a-form-item label="后续建议" name="recommendations">
            <a-textarea v-model:value="completeFormState.recommendations" :rows="2" />
          </a-form-item>
        </a-form>
      </div>
    </a-modal>

    <!-- 维修详情弹窗 -->
    <a-modal
      v-model:visible="detailModalVisible"
      title="维修详情"
      :footer="null"
      width="700px"
    >
      <div v-if="currentMaintenance">
        <div v-if="currentMaintenance.fromSafetyCheck" class="safety-check-alert">
          <a-alert
            message="来自安全检查的工单"
            description="此维修工单由设备安全检查异常自动生成"
            type="info"
            showIcon
          />
        </div>

        <a-descriptions bordered title="基本信息" size="small" :column="2">
          <a-descriptions-item label="设备名称">{{ currentMaintenance.deviceName }}</a-descriptions-item>
          <a-descriptions-item label="维修类型">{{ currentMaintenance.maintenanceType }}</a-descriptions-item>
          <a-descriptions-item label="开始日期">{{ formatDate(currentMaintenance.startDate) }}</a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="getStatusColor(currentMaintenance.status)">{{ currentMaintenance.status }}</a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="维修人员">{{ currentMaintenance.maintenanceStaff }}</a-descriptions-item>
          <a-descriptions-item label="联系电话">{{ currentMaintenance.contactPhone || '无' }}</a-descriptions-item>
          <template v-if="currentMaintenance.faultLevel">
            <a-descriptions-item label="故障等级">
              <a-tag :color="currentMaintenance.faultLevel === '严重' ? 'red' : ''">
                {{ currentMaintenance.faultLevel }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="紧急程度">
              <a-tag :color="currentMaintenance.urgency === '紧急' ? 'orange' : ''">
                {{ currentMaintenance.urgency }}
              </a-tag>
            </a-descriptions-item>
          </template>
        </a-descriptions>
        
        <a-divider />
        
        <a-descriptions bordered title="维修内容" size="small" :column="1">
          <a-descriptions-item label="故障描述/维修内容">{{ currentMaintenance.description }}</a-descriptions-item>
          <a-descriptions-item label="需要更换零部件">{{ currentMaintenance.partsToReplace || '无' }}</a-descriptions-item>
        </a-descriptions>
        
        <template v-if="currentMaintenance.status === '已完成'">
          <a-divider />
          
          <a-descriptions bordered title="维修结果" size="small" :column="2">
            <a-descriptions-item label="实际完成日期">{{ formatDate(currentMaintenance.actualEndDate) }}</a-descriptions-item>
            <a-descriptions-item label="维修结果">
              <a-tag :color="getResultColor(currentMaintenance.result)">{{ currentMaintenance.result }}</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="实际更换零部件" span="2">{{ currentMaintenance.partsReplaced || '无' }}</a-descriptions-item>
            <a-descriptions-item label="维修详情" span="2">{{ currentMaintenance.maintenanceDetails }}</a-descriptions-item>
            <a-descriptions-item label="后续建议" span="2">{{ currentMaintenance.recommendations || '无' }}</a-descriptions-item>
          </a-descriptions>
        </template>
        
        <div style="margin-top: 20px; display: flex; justify-content: flex-end;">
          <a-button @click="detailModalVisible = false">关闭</a-button>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import dayjs from 'dayjs';

// 设备列表，实际应用中应该从设备管理中获取
const deviceList = ref([
  { key: '1', name: '监控摄像头' },
  { key: '2', name: '入侵检测器' },
  { key: '3', name: '门禁系统' },
  { key: '4', name: '报警器' }
]);

interface MaintenanceRecord {
  key: string;
  deviceName: string;
  maintenanceType: string;
  startDate: dayjs.Dayjs | string;
  estimatedEndDate?: dayjs.Dayjs | string;
  actualEndDate?: dayjs.Dayjs | string;
  maintenanceStaff: string;
  contactPhone?: string;
  description: string;
  partsToReplace?: string;
  status: string;
  result?: string;
  partsReplaced?: string;
  maintenanceDetails?: string;
  recommendations?: string;
  fromSafetyCheck?: boolean;
  faultLevel?: string;
  urgency?: string;
}

// 表格列定义
const columns = [
  {
    title: '设备名称',
    dataIndex: 'deviceName',
  },
  {
    title: '维修时间',
    dataIndex: 'startDate',
    customRender: ({ text }: { text: string | dayjs.Dayjs }) => formatDate(text),
    sorter: (a: MaintenanceRecord, b: MaintenanceRecord) => {
      const dateA = dayjs(a.startDate).unix();
      const dateB = dayjs(b.startDate).unix();
      return dateA - dateB;
    },
  },
  {
    title: '维修内容',
    dataIndex: 'description',
    ellipsis: true,
  },
  {
    title: '更换零部件',
    dataIndex: 'partsToReplace',
    ellipsis: true,
  },
  {
    title: '维修人员',
    dataIndex: 'maintenanceStaff',
  },
  {
    title: '状态',
    dataIndex: 'status',
  },
  {
    title: '来源',
    dataIndex: 'source',
  },
  {
    title: '紧急程度',
    dataIndex: 'urgency',
  },
  {
    title: '操作',
    dataIndex: 'action',
  }
];

// 格式化日期
const formatDate = (date: string | dayjs.Dayjs | undefined): string => {
  if (!date) return '';
  if (typeof date === 'string') return date;
  return date.format('YYYY-MM-DD');
};

// 获取状态颜色
const getStatusColor = (status: string): string => {
  switch (status) {
    case '进行中':
      return 'blue';
    case '已完成':
      return 'green';
    default:
      return 'default';
  }
};

// 获取结果颜色
const getResultColor = (result: string | undefined): string => {
  switch (result) {
    case '已解决':
      return 'green';
    case '部分解决':
      return 'orange';
    case '未解决':
      return 'red';
    default:
      return 'default';
  }
};

// 维修记录数据
const maintenanceData = ref<MaintenanceRecord[]>([
  {
    key: '1',
    deviceName: '监控摄像头',
    maintenanceType: '计划保养',
    startDate: '2023-06-05',
    estimatedEndDate: '2023-06-05',
    actualEndDate: '2023-06-05',
    maintenanceStaff: '张三',
    contactPhone: '13800138000',
    description: '定期清洁摄像头镜头，检查连接稳定性',
    status: '已完成',
    result: '已解决',
    maintenanceDetails: '清洁镜头，重新连接电源线，更新了固件',
    recommendations: '建议每季度进行一次清洁和检查',
    fromSafetyCheck: false
  },
  {
    key: '2',
    deviceName: '入侵检测器',
    maintenanceType: '故障维修',
    startDate: '2023-06-10',
    estimatedEndDate: '2023-06-12',
    maintenanceStaff: '李四',
    contactPhone: '13900139000',
    description: '检测器频繁触发虚假警报，需要检修传感器',
    partsToReplace: '红外传感器',
    status: '进行中',
    fromSafetyCheck: true,
    faultLevel: '一般',
    urgency: '常规'
  },
  {
    key: '3',
    deviceName: '门禁系统',
    maintenanceType: '紧急维修',
    startDate: '2023-06-01',
    estimatedEndDate: '2023-06-02',
    actualEndDate: '2023-06-01',
    maintenanceStaff: '王五',
    contactPhone: '13700137000',
    description: '门禁系统无法读取卡片',
    partsToReplace: '读卡器',
    status: '已完成',
    result: '已解决',
    partsReplaced: '更换了读卡器',
    maintenanceDetails: '更换读卡器，重新配置系统',
    recommendations: '建议定期检查读卡器状态',
    fromSafetyCheck: false
  }
]);

// 表单状态
interface FormState {
  deviceName: string;
  maintenanceType: string;
  startDate: dayjs.Dayjs | null;
  estimatedEndDate: dayjs.Dayjs | null;
  maintenanceStaff: string;
  contactPhone: string;
  description: string;
  partsToReplace: string;
}

const formState = reactive<FormState>({
  deviceName: '',
  maintenanceType: '计划保养',
  startDate: null,
  estimatedEndDate: null,
  maintenanceStaff: '',
  contactPhone: '',
  description: '',
  partsToReplace: ''
});

// 完成维修表单状态
interface CompleteFormState {
  actualEndDate: dayjs.Dayjs | null;
  result: string;
  partsReplaced: string;
  maintenanceDetails: string;
  recommendations: string;
}

const completeFormState = reactive<CompleteFormState>({
  actualEndDate: dayjs(),
  result: '已解决',
  partsReplaced: '',
  maintenanceDetails: '',
  recommendations: ''
});

// 模态框可见性
const addModalVisible = ref(false);
const completeModalVisible = ref(false);
const detailModalVisible = ref(false);
const currentMaintenance = ref<MaintenanceRecord | null>(null);

// 从本地存储获取故障报告
const fetchFaultReports = () => {
  try {
    const storedReports = localStorage.getItem('faultReports');
    if (storedReports) {
      const reports = JSON.parse(storedReports);
      
      // 将故障报告转换为维修记录格式并添加到数据中
      reports.forEach((report: any) => {
        // 检查是否已经存在相同的记录
        const existingIndex = maintenanceData.value.findIndex(item => item.key === report.key);
        if (existingIndex === -1) {
          maintenanceData.value.push({
            ...report,
            fromSafetyCheck: true
          });
        }
      });
    }
  } catch (error) {
    console.error('获取故障报告失败', error);
  }
};

// 组件挂载时获取故障报告
onMounted(() => {
  fetchFaultReports();
});

// 显示添加对话框
const showAddModal = () => {
  resetForm();
  addModalVisible.value = true;
};

// 处理添加维修记录
const handleAddOk = () => {
  if (!formState.deviceName || !formState.maintenanceType || !formState.startDate || !formState.maintenanceStaff || !formState.description) {
    message.error('请填写所有必填项');
    return;
  }

  const newMaintenance: MaintenanceRecord = {
    key: Date.now().toString(),
    deviceName: formState.deviceName,
    maintenanceType: formState.maintenanceType,
    startDate: formState.startDate as dayjs.Dayjs,
    estimatedEndDate: formState.estimatedEndDate,
    maintenanceStaff: formState.maintenanceStaff,
    contactPhone: formState.contactPhone,
    description: formState.description,
    partsToReplace: formState.partsToReplace,
    status: '进行中',
    fromSafetyCheck: false
  };

  maintenanceData.value.push(newMaintenance);
  message.success('维修记录添加成功');
  addModalVisible.value = false;
  resetForm();
};

const handleAddCancel = () => {
  addModalVisible.value = false;
  resetForm();
};

// 显示完成维修对话框
const showCompleteModal = (record: MaintenanceRecord) => {
  currentMaintenance.value = record;
  completeFormState.actualEndDate = dayjs();
  completeFormState.result = '已解决';
  completeFormState.partsReplaced = record.partsToReplace || '';
  completeFormState.maintenanceDetails = '';
  completeFormState.recommendations = '';
  completeModalVisible.value = true;
};

// 处理完成维修
const handleCompleteOk = () => {
  if (!currentMaintenance.value || !completeFormState.actualEndDate || !completeFormState.maintenanceDetails) {
    message.error('请填写所有必填项');
    return;
  }

  // 更新维修记录
  const index = maintenanceData.value.findIndex(item => item.key === currentMaintenance.value?.key);
  if (index !== -1) {
    maintenanceData.value[index] = {
      ...maintenanceData.value[index],
      status: '已完成',
      actualEndDate: completeFormState.actualEndDate,
      result: completeFormState.result,
      partsReplaced: completeFormState.partsReplaced,
      maintenanceDetails: completeFormState.maintenanceDetails,
      recommendations: completeFormState.recommendations
    };
    
    message.success('维修记录已更新');
  }
  
  completeModalVisible.value = false;
};

const handleCompleteCancel = () => {
  completeModalVisible.value = false;
};

// 显示删除确认
const showDeleteConfirm = (record: MaintenanceRecord) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除设备"${record.deviceName}"的维修记录吗？此操作无法撤销。`,
    okText: '确认',
    okType: 'danger',
    cancelText: '取消',
    onOk() {
      deleteMaintenance(record.key);
    },
  });
};

// 删除维修记录
const deleteMaintenance = (key: string) => {
  maintenanceData.value = maintenanceData.value.filter(item => item.key !== key);
  message.success('维修记录已删除');
};

// 查看维修详情
const viewMaintenanceDetails = (record: MaintenanceRecord) => {
  currentMaintenance.value = record;
  detailModalVisible.value = true;
};

// 重置表单
const resetForm = () => {
  formState.deviceName = '';
  formState.maintenanceType = '计划保养';
  formState.startDate = null;
  formState.estimatedEndDate = null;
  formState.maintenanceStaff = '';
  formState.contactPhone = '';
  formState.description = '';
  formState.partsToReplace = '';
};
</script>

<style scoped>
.device-log-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-buttons {
  display: flex;
  gap: 10px;
}

.action-buttons .ant-btn {
  margin-right: 8px;
}

.safety-check-alert {
  margin-bottom: 20px;
}
</style>
