<template>
    <div class="personnel-container">
        <!-- Action buttons -->
        <div class="action-buttons">
            <a-button type="primary" @click="showAddModal">添加人员</a-button>
            <a-button danger :disabled="!hasSelected" @click="confirmDelete">批量删除</a-button>
            <span v-if="hasSelected" style="margin-left: 8px">
                已选择 {{ selectedRowKeys.length }} 项
            </span>
        </div>

        <!-- Personnel table -->
        <a-table :columns="columns" :data-source="data"
            :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
            :pagination="{ pageSize: 10 }">
            <template #headerCell="{ column }">
                <template v-if="column.key === 'name'">
                    <span>
                        <user-outlined />
                        {{ column.title }}
                    </span>
                </template>
            </template>

            <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'name'">
                    <a>
                        {{ record.name }}
                    </a>
                </template>
                <template v-else-if="column.key === 'tags'">
                    <span>
                        <a-tag v-for="tag in record.tags" :key="tag"
                            :color="tag === 'teacher' ? 'blue' : tag === 'student' ? 'green' : 'orange'">
                            {{ tag === 'teacher' ? '教师' : tag === 'student' ? '学生' : '其他' }}
                        </a-tag>
                    </span>
                </template>
                <template v-else-if="column.key === 'face'">
                    <div v-if="record.faceImage">
                        <img :src="record.faceImage" alt="人脸照片" class="face-image" />
                    </div>
                    <div v-else>
                        <a-button size="small" @click="uploadFace(record)">上传人脸</a-button>
                    </div>
                </template>
                <template v-else-if="column.key === 'action'">
                    <span>
                        <a-button type="link" @click="editPersonnel(record)">编辑</a-button>
                        <a-button type="link" danger @click="deleteSingle(record)">删除</a-button>
                    </span>
                </template>
            </template>
        </a-table>

        <!-- Add/Edit Personnel Modal -->
        <a-modal v-model:visible="modalVisible" :title="isEditing ? '编辑人员信息' : '添加人员'" @ok="handleModalOk"
            @cancel="modalVisible = false" width="800px">
            <a-form :model="formState" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
                <a-form-item label="姓名" required>
                    <a-input v-model:value="formState.name" placeholder="请输入姓名" />
                </a-form-item>
                <a-form-item label="性别" required>
                    <a-radio-group v-model:value="formState.sex">
                        <a-radio value="男">男</a-radio>
                        <a-radio value="女">女</a-radio>
                    </a-radio-group>
                </a-form-item>
                <a-form-item label="年龄" required>
                    <a-input-number v-model:value="formState.age" :min="1" :max="100" />
                </a-form-item>
                <a-form-item label="班级/部门" required>
                    <a-input v-model:value="formState.class" placeholder="请输入班级或部门" />
                </a-form-item>
                <a-form-item label="角色类型" required>
                    <a-select v-model:value="formState.role" placeholder="请选择角色类型">
                        <a-select-option value="teacher">教师</a-select-option>
                        <a-select-option value="student">学生</a-select-option>
                        <a-select-option value="other">其他</a-select-option>
                    </a-select>
                </a-form-item>

                <!-- Student specific fields -->
                <template v-if="formState.role === 'student'">
                    <a-form-item label="专业">
                        <a-input v-model:value="formState.major" placeholder="请输入专业" />
                    </a-form-item>
                    <a-form-item label="年级">
                        <a-input v-model:value="formState.grade" placeholder="请输入年级" />
                    </a-form-item>
                    <a-form-item label="学号" required>
                        <a-input v-model:value="formState.id" placeholder="请输入学号" />
                    </a-form-item>
                </template>

                <!-- Teacher specific field -->
                <template v-if="formState.role === 'teacher'">
                    <a-form-item label="教职号" required>
                        <a-input v-model:value="formState.id" placeholder="请输入教职号" />
                    </a-form-item>
                </template>

                <a-form-item label="联系方式" required>
                    <a-input v-model:value="formState.phone" placeholder="请输入联系方式" />
                </a-form-item>

                <a-form-item label="人脸信息">
                    <a-upload list-type="picture-card" :max-count="1" @change="handleFaceUpload">
                        <div v-if="!formState.faceImage">
                            <upload-outlined />
                            <div style="margin-top: 8px">上传照片</div>
                        </div>
                    </a-upload>
                    <img v-if="formState.faceImage" :src="formState.faceImage" class="preview-img" />
                </a-form-item>
            </a-form>
        </a-modal>

        <!-- Face Upload Modal -->
        <a-modal v-model:visible="faceModalVisible" title="上传人脸照片" @ok="handleFaceModalOk"
            @cancel="faceModalVisible = false">
            <a-upload list-type="picture-card" :max-count="1" @change="handleFaceUpload">
                <div v-if="!currentFaceImage">
                    <upload-outlined />
                    <div style="margin-top: 8px">上传照片</div>
                </div>
            </a-upload>
            <img v-if="currentFaceImage" :src="currentFaceImage" class="preview-img" />
        </a-modal>
    </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed } from 'vue';
import {
    UserOutlined,
    UploadOutlined
} from '@ant-design/icons-vue';
import { message, Modal } from 'ant-design-vue';

// 表格列定义
const columns = [
    {
        title: '姓名',
        dataIndex: 'name',
        key: 'name',
    },
    {
        title: '性别',
        dataIndex: 'sex',
        key: 'sex',
        filters: [
            { text: '男', value: '男' },
            { text: '女', value: '女' },
        ],
        onFilter: (value, record) => record.sex === value,
        filterMultiple: false,
    },
    {
        title: '年龄',
        dataIndex: 'age',
        key: 'age',
        sorter: (a, b) => a.age - b.age,
    },
    {
        title: '班级/部门',
        dataIndex: 'class',
        key: 'class',
    },
    {
        title: '专业',
        dataIndex: 'major',
        key: 'major',
        customFilterDropdown: true,
    },
    {
        title: '年级',
        dataIndex: 'grade',
        key: 'grade',
    },
    {
        title: '学号/教职号',
        dataIndex: 'id',
        key: 'id',
    },
    {
        title: '联系方式',
        dataIndex: 'phone',
        key: 'phone',
    },
    {
        title: '角色权限',
        key: 'tags',
        dataIndex: 'tags',
        filters: [
            { text: '教师', value: 'teacher' },
            { text: '学生', value: 'student' },
            { text: '其他', value: 'other' },
        ],
        onFilter: (value, record) => record.tags.includes(value),
    },
    {
        title: '人脸信息',
        key: 'face',
    },
    {
        title: '头像',
        key: 'avater',
    },
    {
        title: '操作',
        key: 'action',
    }
];

// 示例数据
const data = ref([
    {
        key: '1',
        name: '李教授',
        sex: '男',
        age: 45,
        class: '计算机科学与技术系',
        major: "计算机科学",
        id: "T20010001",
        phone: "13800138000",
        tags: ['teacher'],
        faceImage: null
    },
    {
        key: '2',
        name: '张三',
        sex: '男',
        age: 20,
        class: '计算机2班',
        major: "计算机科学",
        grade: "2022级",
        id: "S202200101",
        phone: "13900139000",
        tags: ['student'],
        faceImage: null
    },
    {
        key: '3',
        name: '李四',
        sex: '女',
        age: 21,
        class: '计算机1班',
        major: "软件工程",
        grade: "2021级",
        id: "S202100201",
        phone: "13700137000",
        tags: ['student'],
        faceImage: null
    },
    {
        key: '4',
        name: '王维修',
        sex: '男',
        age: 35,
        class: '设备部',
        id: "O20050001",
        phone: "13600136000",
        tags: ['other'],
        faceImage: null
    }
]);

// 表格选择状态
const selectedRowKeys = ref([]);
const onSelectChange = (keys) => {
    selectedRowKeys.value = keys;
};
const hasSelected = computed(() => selectedRowKeys.value.length > 0);

// 表单状态
const modalVisible = ref(false);
const isEditing = ref(false);
const currentEditKey = ref(null);
const formState = reactive({
    name: '',
    sex: '男',
    age: 20,
    class: '',
    major: '',
    grade: '',
    id: '',
    phone: '',
    role: 'student',
    faceImage: null
});

// 人脸上传状态
const faceModalVisible = ref(false);
const currentFaceRecord = ref(null);
const currentFaceImage = ref(null);

// 重置表单
const resetForm = () => {
    formState.name = '';
    formState.sex = '男';
    formState.age = 20;
    formState.class = '';
    formState.major = '';
    formState.grade = '';
    formState.id = '';
    formState.phone = '';
    formState.role = 'student';
    formState.faceImage = null;
};

// 打开添加人员模态框
const showAddModal = () => {
    isEditing.value = false;
    resetForm();
    modalVisible.value = true;
};

// 打开编辑人员模态框
const editPersonnel = (record) => {
    isEditing.value = true;
    currentEditKey.value = record.key;

    // 填充表单数据
    formState.name = record.name;
    formState.sex = record.sex;
    formState.age = record.age;
    formState.class = record.class;
    formState.major = record.major || '';
    formState.grade = record.grade || '';
    formState.id = record.id;
    formState.phone = record.phone;
    formState.role = record.tags[0];
    formState.faceImage = record.faceImage;

    modalVisible.value = true;
};

// 处理模态框确认
const handleModalOk = () => {
    // 表单验证
    if (!formState.name || !formState.class || !formState.id || !formState.phone) {
        message.error('请填写必填字段');
        return;
    }

    const newRecord = {
        name: formState.name,
        sex: formState.sex,
        age: formState.age,
        class: formState.class,
        major: formState.major,
        grade: formState.grade,
        id: formState.id,
        phone: formState.phone,
        tags: [formState.role],
        faceImage: formState.faceImage
    };

    if (isEditing.value) {
        // 编辑现有记录
        const index = data.value.findIndex(item => item.key === currentEditKey.value);
        if (index !== -1) {
            newRecord.key = currentEditKey.value;
            data.value.splice(index, 1, newRecord);
            message.success('人员信息已更新');
        }
    } else {
        // 添加新记录
        newRecord.key = Date.now().toString();
        data.value.push(newRecord);
        message.success('人员已添加');
    }

    modalVisible.value = false;
};

// 删除单个人员
const deleteSingle = (record) => {
    Modal.confirm({
        title: '确认删除',
        content: `确定要删除"${record.name}"吗？`,
        okText: '确认',
        cancelText: '取消',
        onOk: () => {
            data.value = data.value.filter(item => item.key !== record.key);
            message.success('已删除');
        }
    });
};

// 批量删除人员
const confirmDelete = () => {
    Modal.confirm({
        title: '确认批量删除',
        content: `确定要删除选中的 ${selectedRowKeys.value.length} 项吗？`,
        okText: '确认',
        cancelText: '取消',
        onOk: () => {
            data.value = data.value.filter(item => !selectedRowKeys.value.includes(item.key));
            selectedRowKeys.value = [];
            message.success('批量删除成功');
        }
    });
};

// 上传人脸照片
const uploadFace = (record) => {
    currentFaceRecord.value = record;
    currentFaceImage.value = record.faceImage;
    faceModalVisible.value = true;
};

// 处理人脸上传
const handleFaceUpload = (info) => {
    if (info.file.status === 'done' || info.file.status === 'uploading') {
        // 在实际应用中，这里应该处理真实的上传逻辑
        // 这里我们使用一个模拟的 base64 图像
        const reader = new FileReader();
        reader.readAsDataURL(info.file.originFileObj);
        reader.onload = () => {
            if (faceModalVisible.value) {
                currentFaceImage.value = reader.result;
            } else {
                formState.faceImage = reader.result;
            }
        };
    }
};

// 处理人脸模态框确认
const handleFaceModalOk = () => {
    if (currentFaceImage.value && currentFaceRecord.value) {
        // 更新人脸信息
        const index = data.value.findIndex(item => item.key === currentFaceRecord.value.key);
        if (index !== -1) {
            data.value[index].faceImage = currentFaceImage.value;
            message.success('人脸信息已更新');
        }
    } else {
        message.error('请先上传人脸照片');
        return;
    }

    faceModalVisible.value = false;
};
</script>

<style scoped>
.personnel-container {
    padding: 16px;
}

.action-buttons {
    margin-bottom: 16px;
}

.action-buttons .ant-btn {
    margin-right: 8px;
}

.face-image {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 4px;
}

.preview-img {
    width: 100px;
    height: 100px;
    object-fit: cover;
    border-radius: 4px;
}
</style>
