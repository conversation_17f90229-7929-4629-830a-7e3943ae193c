import { createRouter, createWebHistory } from "vue-router";

// 认证工具函数
const isAuthenticated = () => {
    // 检查localStorage中是否有token
    const token = localStorage.getItem('userToken');
    const userInfo = localStorage.getItem('userInfo');
    return !!(token && userInfo);
};



const router = createRouter({
    history: createWebHistory(),
    routes: [
        {
            path: '/',
            redirect: '/login'
        },
        {
            path: '/login',
            name: 'Login',
            component: () => import('../components/Login.vue'),
            meta: { requiresAuth: false }
        },
        {
            path: '/home',
            name: 'Home',
            component: () => import('../components/home.vue'),
            meta: { requiresAuth: true }
        },
        {
            path: '/manage',
            name: 'Manage',
            component: () => import('../components/manage.vue'),
            meta: { requiresAuth: true }
        },
        {
            path: '/per',
            name: 'PersonInfo',
            component: () => import('../components/Per_Info.vue'),
            meta: { requiresAuth: true }
        },
        {
            path: '/device_Info',
            name: 'DeviceInfo',
            component: () => import('../components/Device_Info.vue'),
            meta: { requiresAuth: true }
        },
        {
            path: '/device_check',
            name: 'DeviceCheck',
            component: () => import('../components/Device_check.vue'),
            meta: { requiresAuth: true }
        },
        {
            path: '/device_log',
            name: 'DeviceLog',
            component: () => import('../components/Device_log.vue'),
            meta: { requiresAuth: true }
        },
        {
            path: '/attendance',
            name: 'Attendance',
            component: () => import('../components/Attendance.vue'),
            meta: { requiresAuth: true }
        },
        {
            path: '/chat',
            name: 'Chat',
            component: () => import('../components/Chat.vue'),
            meta: { requiresAuth: true }
        },
        {
            path: '/:pathMatch(.*)*',
            name: 'NotFound',
            redirect: '/login'
        }
    ]
});

// 路由守卫
router.beforeEach((to, _from, next) => {
    // 检查目标路由是否需要认证
    if (to.meta.requiresAuth) {
        // 需要认证的路由，检查是否已登录
        if (isAuthenticated()) {
            // 已登录，允许访问
            next();
        } else {
            // 未登录，重定向到登录页
            console.log('用户未登录，重定向到登录页');
            next({
                path: '/login',
                query: { redirect: to.fullPath } // 保存原始路径，登录后可以重定向回来
            });
        }
    } else {
        // 不需要认证的路由（如登录页）
        if (to.path === '/login' && isAuthenticated()) {
            // 如果已登录用户访问登录页，重定向到首页
            next('/home');
        } else {
            // 允许访问
            next();
        }
    }
});

export default router;
